//
//  Logger+Extensions.swift
//  iOSProject
//
//  Created by Code Quality Review on 10/08/2025.
//

import OSLog
import Foundation

/// Centralized logging system for the iOS application
/// Replaces scattered print statements with structured logging
extension Logger {
    
    // MARK: - Logger Categories
    
    /// Logger for UI-related events and interactions
    static let ui = Logger(subsystem: Bundle.main.bundleIdentifier ?? "com.demo.app.iOSProject", category: "UI")
    
    /// Logger for navigation and routing events
    static let navigation = Logger(subsystem: Bundle.main.bundleIdentifier ?? "com.demo.app.iOSProject", category: "Navigation")
    
    /// Logger for image loading and asset management
    static let imageLoading = Logger(subsystem: Bundle.main.bundleIdentifier ?? "com.demo.app.iOSProject", category: "ImageLoading")
    
    /// Logger for authentication and user management
    static let auth = Logger(subsystem: Bundle.main.bundleIdentifier ?? "com.demo.app.iOSProject", category: "Authentication")
    
    /// Logger for data operations and API calls
    static let data = Logger(subsystem: Bundle.main.bundleIdentifier ?? "com.demo.app.iOSProject", category: "Data")
    
    /// Logger for theme and appearance changes
    static let theme = Logger(subsystem: Bundle.main.bundleIdentifier ?? "com.demo.app.iOSProject", category: "Theme")
    
    /// Logger for validation and form processing
    static let validation = Logger(subsystem: Bundle.main.bundleIdentifier ?? "com.demo.app.iOSProject", category: "Validation")
    
    /// Logger for dependency injection and service resolution
    static let dependencyInjection = Logger(subsystem: Bundle.main.bundleIdentifier ?? "com.demo.app.iOSProject", category: "DependencyInjection")
    
    /// Logger for business logic and use cases
    static let business = Logger(subsystem: Bundle.main.bundleIdentifier ?? "com.demo.app.iOSProject", category: "Business")
    
    /// Logger for network operations
    static let network = Logger(subsystem: Bundle.main.bundleIdentifier ?? "com.demo.app.iOSProject", category: "Network")
    
    /// Logger for performance monitoring
    static let performance = Logger(subsystem: Bundle.main.bundleIdentifier ?? "com.demo.app.iOSProject", category: "Performance")
    
    /// Logger for general application events
    static let app = Logger(subsystem: Bundle.main.bundleIdentifier ?? "com.demo.app.iOSProject", category: "Application")
}

// MARK: - Convenience Logging Methods
extension Logger {
    
    /// Log user interaction events
    func logUserInteraction(_ action: String, details: [String: Any]? = nil) {
        let detailsString = details?.map { "\($0.key): \($0.value)" }.joined(separator: ", ") ?? ""
        self.info("User Interaction: \(action) \(detailsString)")
    }
    
    /// Log navigation events
    func logNavigation(from: String, to: String) {
        self.info("Navigation: \(from) → \(to)")
    }
    
    /// Log data loading events
    func logDataLoading(_ operation: String, duration: TimeInterval? = nil) {
        if let duration = duration {
            self.info("Data Loading: \(operation) completed in \(String(format: "%.2f", duration))s")
        } else {
            self.info("Data Loading: \(operation) started")
        }
    }
    
    /// Log error with context
    func logError(_ error: Error, context: String) {
        self.error("Error in \(context): \(error.localizedDescription)")
    }
    
    /// Log warning with context
    func logWarning(_ message: String, context: String) {
        self.warning("Warning in \(context): \(message)")
    }
    
    /// Log performance metrics
    func logPerformance(_ operation: String, duration: TimeInterval) {
        self.info("Performance: \(operation) took \(String(format: "%.3f", duration))s")
    }
    
    /// Log feature usage
    func logFeatureUsage(_ feature: String, parameters: [String: Any]? = nil) {
        let paramString = parameters?.map { "\($0.key): \($0.value)" }.joined(separator: ", ") ?? ""
        self.info("Feature Usage: \(feature) \(paramString)")
    }
}

// MARK: - Debug Logging (Only in Debug builds)
extension Logger {
    
    /// Log debug information (only in debug builds)
    func debugMessage(_ message: String) {
        #if DEBUG
        self.debug("\(message)")
        #endif
    }
    
    /// Log verbose debug information with file and line
    func verbose(_ message: String, file: String = #file, line: Int = #line) {
        #if DEBUG
        let fileName = (file as NSString).lastPathComponent
        self.debug("[\(fileName):\(line)] \(message)")
        #endif
    }
}

// MARK: - Structured Logging Helpers
struct LogContext {
    let userId: String?
    let sessionId: String?
    let feature: String?
    let screen: String?
    
    init(userId: String? = nil, sessionId: String? = nil, feature: String? = nil, screen: String? = nil) {
        self.userId = userId
        self.sessionId = sessionId
        self.feature = feature
        self.screen = screen
    }
    
    var contextString: String {
        var components: [String] = []
        if let userId = userId { components.append("userId: \(userId)") }
        if let sessionId = sessionId { components.append("sessionId: \(sessionId)") }
        if let feature = feature { components.append("feature: \(feature)") }
        if let screen = screen { components.append("screen: \(screen)") }
        return components.isEmpty ? "" : "[\(components.joined(separator: ", "))]"
    }
}

extension Logger {
    
    /// Log with structured context
    func info(_ message: String, context: LogContext) {
        self.info("\(context.contextString) \(message)")
    }
    
    /// Log error with structured context
    func error(_ message: String, context: LogContext) {
        self.error("\(context.contextString) \(message)")
    }
    
    /// Log warning with structured context
    func warning(_ message: String, context: LogContext) {
        self.warning("\(context.contextString) \(message)")
    }
}

// MARK: - Analytics Integration Helper
extension Logger {
    
    /// Log events that should also be sent to analytics
    func logAnalyticsEvent(_ eventName: String, parameters: [String: Any]? = nil) {
        // Log locally
        let paramString = parameters?.map { "\($0.key): \($0.value)" }.joined(separator: ", ") ?? ""
        self.info("Analytics Event: \(eventName) \(paramString)")
        
        // TODO: Integrate with analytics service (Firebase, Mixpanel, etc.)
        // AnalyticsService.shared.track(eventName, parameters: parameters)
    }
}

// MARK: - Performance Measurement
class PerformanceLogger {
    private let logger: Logger
    private let operation: String
    private let startTime: CFAbsoluteTime
    
    init(logger: Logger, operation: String) {
        self.logger = logger
        self.operation = operation
        self.startTime = CFAbsoluteTimeGetCurrent()
        logger.info("Performance: \(operation) started")
    }
    
    func finish() {
        let duration = CFAbsoluteTimeGetCurrent() - startTime
        logger.logPerformance(operation, duration: duration)
    }
    
    deinit {
        finish()
    }
}

// MARK: - Usage Examples and Documentation
/*
 Usage Examples:
 
 // Basic logging
 Logger.ui.info("Button tapped: Login")
 Logger.navigation.info("Navigated to Dashboard")
 Logger.imageLoading.warning("Image not found: \(imageName), using placeholder")
 
 // User interaction logging
 Logger.ui.logUserInteraction("App Store button tapped", details: ["screen": "Home"])
 
 // Navigation logging
 Logger.navigation.logNavigation(from: "Authentication", to: "Dashboard")
 
 // Error logging
 Logger.data.logError(error, context: "Loading user profile")
 
 // Performance logging
 let perfLogger = PerformanceLogger(logger: Logger.performance, operation: "Data Loading")
 // ... perform operation ...
 perfLogger.finish()
 
 // Structured logging with context
 let context = LogContext(userId: "123", feature: "Newsletter", screen: "Home")
 Logger.business.info("Newsletter subscription attempted", context: context)
 
 // Analytics events
 Logger.app.logAnalyticsEvent("newsletter_subscription", parameters: ["email": email])
 */
