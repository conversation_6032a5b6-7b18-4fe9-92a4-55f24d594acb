<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>UIAppFonts</key>
	<array>
		<string>PoppinsRegular.ttf</string>
		<string>PoppinsMedium.ttf</string>
		<string>InterSemiBold.ttf</string>
		<string>InterRegular.ttf</string>
		<string>InterMedium.ttf</string>
	</array>

	<!-- Azure Communication Services Permissions -->
	<key>NSCameraUsageDescription</key>
	<string>This app needs camera access to enable video calling through Azure Communication Services</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>This app needs microphone access to enable voice calling through Azure Communication Services</string>
    <key>NSCameraUsageDescription</key>
    <string></string>
    <key>NSMicrophoneUsageDescription</key>
    <string></string>
</dict>
</plist>
