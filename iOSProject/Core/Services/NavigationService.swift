import SwiftUI
import Foundation

// MARK: - Navigation Service
class NavigationService: NavigationServiceProtocol, ObservableObject {
   
    
    // MARK: - Published Properties
    @Published var currentView: AppView = .authentication
    @Published var navigationPath = NavigationPath()
    // Per‑tab navigation paths for dynamic TabView content
    @Published var tabPaths: [Tab: NavigationPath] = [:]
    @Published var toast: [Toast] = .init()
   
    
    // MARK: - Snackbar Methods
    func showToast(_ value: Toast) {
        toast.append(value)
    }
    
    
    func clearToasts() {
        toast.removeAll()
    }
    
    // MARK: - Dynamic Tab Navigation Helpers
    /// Ensure a path exists for a tab
    func ensurePath(for tab: Tab) {
        if tabPaths[tab] == nil { tabPaths[tab] = NavigationPath() }
    }

    /// Binding to a specific tab's NavigationPath (use in NavigationStack)
    func binding(for tab: Tab) -> Binding<NavigationPath> {
        Binding(
            get: { self.tabPaths[tab] ?? NavigationPath() },
            set: { self.tabPaths[tab] = $0 }
        )
    }

    /// Programmatic navigation within a specific tab
    func navigate(_ destination: AppDestination, in tab: Tab) {
        ensurePath(for: tab)
        tabPaths[tab]?.append(destination)
    }

    func pop(in tab: Tab) {
        guard var path = tabPaths[tab], !path.isEmpty else { return }
        path.removeLast()
        tabPaths[tab] = path
    }

    func popToRoot(in tab: Tab) {
        tabPaths[tab] = NavigationPath()
    }

    func canGoBack(in tab: Tab) -> Bool {
        !(tabPaths[tab] ?? NavigationPath()).isEmpty
    }
    
    // MARK: - Navigation Methods
    func navigateToMainNavigation() {
        DispatchQueue.main.async {
            self.currentView = .mainNavigation
            self.tabPaths = [:]
          
        }
    }
    
    func navigateToAuthentication() {
        DispatchQueue.main.async {
            self.currentView = .authentication
            self.tabPaths = [:]
        }
    }
    
    func navigateBack() {
        DispatchQueue.main.async {
            if !self.navigationPath.isEmpty {
                self.navigationPath.removeLast()
            }
        }
    }
    
    // MARK: - Additional Navigation Methods
    func navigateToProfile() {
        DispatchQueue.main.async {
            self.navigationPath.append(AppDestination.profile)
        }
    }
    
    func navigateToSettings() {
        DispatchQueue.main.async {
            self.navigationPath.append(AppDestination.settings)
        }
    }
    
    func navigateToNewsDetail(newsId: String) {
        DispatchQueue.main.async {
            self.navigationPath.append(AppDestination.newsDetail(id: newsId))
        }
    }
    
    func navigateToNotifications() {
        DispatchQueue.main.async {
            self.navigationPath.append(AppDestination.notifications)
        }
    }
    
    func popToRoot() {
        DispatchQueue.main.async {
            self.navigationPath = NavigationPath()
        }
    }
    
    func canGoBack() -> Bool {
        return !navigationPath.isEmpty
    }
}

// MARK: - App Destination Enum
enum AppDestination: Hashable {
    case profile
    case settings
    case newsDetail(id: String)
    case notifications
    case favourites
    
    // MARK: - Hashable Conformance
    func hash(into hasher: inout Hasher) {
        switch self {
        case .profile:
            hasher.combine("profile")
        case .settings:
            hasher.combine("settings")
        case .newsDetail(let id):
            hasher.combine("newsDetail")
            hasher.combine(id)
        case .notifications:
            hasher.combine("notifications")
        case .favourites:
            hasher.combine("favourites")
        }
    }
    
    static func == (lhs: AppDestination, rhs: AppDestination) -> Bool {
        switch (lhs, rhs) {
        case (.profile, .profile),
             (.settings, .settings),
             (.notifications, .notifications),
             (.favourites, .favourites):
            return true
        case (.newsDetail(let lhsId), .newsDetail(let rhsId)):
            return lhsId == rhsId
        default:
            return false
        }
    }
}
