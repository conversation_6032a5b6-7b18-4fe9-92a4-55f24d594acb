import Foundation

// MARK: - Validation Service
class ValidationService: ValidationServiceProtocol {
    
    func validateEmail(_ email: String) -> ValidationResult {
        // Check if email is empty
        guard !email.isEmpty else {
            return .invalid(AppConstants.Strings.emailRequired)
        }

        // Check email format
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", AppConstants.RegularExpressions.email)

        guard emailPredicate.evaluate(with: email) else {
            return .invalid(AppConstants.Strings.emailInvalid)
        }

        // Check email length
        guard email.count <= AppConstants.Numbers.maxEmailLength else {
            return .invalid(AppConstants.Strings.emailTooLong)
        }

        return .valid
    }
    
    func validatePassword(_ password: String) -> ValidationResult {
        // Check if password is empty
        guard !password.isEmpty else {
            return .invalid(AppConstants.Strings.passwordRequired)
        }

        // Check minimum length
        guard password.count >= AppConstants.Numbers.minPasswordLength else {
            return .invalid(AppConstants.Strings.passwordTooShort)
        }

        // Check maximum length
        guard password.count <= AppConstants.Numbers.maxPasswordLength else {
            return .invalid(AppConstants.Strings.passwordTooLong)
        }

        // Check for at least one letter
        let letterPredicate = NSPredicate(format: "SELF MATCHES %@", AppConstants.RegularExpressions.password)
        guard letterPredicate.evaluate(with: password) else {
            return .invalid(AppConstants.Strings.passwordMustContainLetter)
        }

        return .valid
    }
    
    // MARK: - Additional Validation Methods
    func validateName(_ name: String) -> ValidationResult {
        guard !name.isEmpty else {
            return .invalid(AppConstants.Strings.nameRequired)
        }

        guard name.count >= AppConstants.Numbers.minNameLength else {
            return .invalid(AppConstants.Strings.nameTooShort)
        }

        guard name.count <= AppConstants.Numbers.maxNameLength else {
            return .invalid(AppConstants.Strings.nameTooLong)
        }

        // Check for valid characters (letters, spaces, hyphens, apostrophes)
        let namePredicate = NSPredicate(format: "SELF MATCHES %@", AppConstants.RegularExpressions.name)
        guard namePredicate.evaluate(with: name) else {
            return .invalid(AppConstants.Strings.nameInvalidCharacters)
        }

        return .valid
    }
    
    func validatePhoneNumber(_ phoneNumber: String) -> ValidationResult {
        guard !phoneNumber.isEmpty else {
            return .invalid(AppConstants.Strings.phoneRequired)
        }

        // Remove all non-digit characters for validation
        let digitsOnly = phoneNumber.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()

        guard digitsOnly.count >= AppConstants.Numbers.minPhoneDigits else {
            return .invalid(AppConstants.Strings.phoneTooShort)
        }

        guard digitsOnly.count <= AppConstants.Numbers.maxPhoneDigits else {
            return .invalid(AppConstants.Strings.phoneTooLong)
        }

        return .valid
    }
    
    func validateCredentials(email: String, password: String) -> (email: ValidationResult, password: ValidationResult) {
        return (
            email: validateEmail(email),
            password: validatePassword(password)
        )
    }
}
