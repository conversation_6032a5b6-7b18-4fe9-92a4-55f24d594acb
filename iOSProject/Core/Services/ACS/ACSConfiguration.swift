//
//  ACSConfiguration.swift
//  iOSProject
//
//  Created by Azure Communication Services Integration on 11/08/2025.
//

import Foundation

// MARK: - ACS Configuration

/// Simplified configuration for Azure Communication Services
struct ACSConfiguration: Codable {
    let connectionString: String
    var displayName: String
    let userAccessToken: String

    /// Default configuration with hardcoded values for now
    static let `default` = ACSConfiguration(
        connectionString: "endpoint=https://your-acs-resource.communication.azure.com/;accesskey=your-access-key",
        displayName: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Rabbin",
        userAccessToken: ""
    )
    
    /// Load configuration from a custom plist in the main bundle using Codable
    static func fromPlist(named name: String = "ACSConfig") -> ACSConfiguration {
        guard let url = Bundle.main.url(forResource: name, withExtension: "plist"),
              let data = try? Data(contentsOf: url),
              let model = try? PropertyListDecoder().decode(ACSConfiguration.self, from: data) else {
            return .default
        }
        return model
    }

    /// Convenience: default external plist name is "ACSConfig.plist"
    static var fromCustomPlist: ACSConfiguration { fromPlist() }

    /// Check if configuration has valid credentials
    var isValid: Bool {
        return !connectionString.isEmpty && connectionString.contains("endpoint=")
    }

    /// Check if configuration is using development/placeholder credentials
    var isDevelopmentMode: Bool {
        return connectionString.contains("your-acs-resource") || connectionString.contains("your-access-key")
    }
}

// MARK: - ACS Call State

/// Represents the current state of an ACS call
enum ACSCallState {
    case idle
    case connecting
    case connected
    case disconnecting
    case disconnected
    case failed(ACSError)
}

// MARK: - ACS Error Types

/// Simplified error handling for ACS operations
enum ACSError: Error, LocalizedError {
    case configurationError
    case permissionDenied
    case callFailed
    case networkError

    var errorDescription: String? {
        switch self {
        case .configurationError:
            return "Configuration error"
        case .permissionDenied:
            return "Permission denied"
        case .callFailed:
            return "Call failed"
        case .networkError:
            return "Network error"
        }
    }
}
