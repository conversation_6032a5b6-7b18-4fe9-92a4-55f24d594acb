import Foundation

// MARK: - News Use Case
class NewsUseCase: NewsUseCaseProtocol {
    private let repository: NewsRepositoryProtocol
    
    init(repository: NewsRepositoryProtocol) {
        self.repository = repository
    }
    
    func getNews() async throws -> [NewsItem] {
        // Delegate to repository
        let news = try await repository.getNews()
        
        // Business logic: Sort news by publication date (newest first)
        return news.sorted { $0.publishedAt > $1.publishedAt }
    }
    
    func getNewsItem(id: String) async throws -> NewsItem {
        // Business logic validation
        guard !id.isEmpty else {
            throw NewsUseCaseError.emptyNewsId
        }
        
        // Delegate to repository
        return try await repository.getNewsItem(id: id)
    }
    
    // MARK: - Additional Business Logic Methods
    func getNewsByCategory(_ category: String) async throws -> [NewsItem] {
        guard !category.isEmpty else {
            throw NewsUseCaseError.emptyCategoryName
        }
        
        let allNews = try await getNews()
        return allNews.filter { $0.category.rawValue.lowercased() == category.lowercased() }
    }
    
    func getRecentNews(limit: Int = 5) async throws -> [NewsItem] {
        guard limit > 0 else {
            throw NewsUseCaseError.invalidLimit
        }
        
        let allNews = try await getNews()
        return Array(allNews.prefix(limit))
    }
    
    func searchNews(query: String) async throws -> [NewsItem] {
        guard !query.isEmpty else {
            throw NewsUseCaseError.emptySearchQuery
        }
        
        let allNews = try await getNews()
        let lowercaseQuery = query.lowercased()
        
        return allNews.filter { newsItem in
            newsItem.title.lowercased().contains(lowercaseQuery) ||
            newsItem.description.lowercased().contains(lowercaseQuery) ||
            newsItem.content.lowercased().contains(lowercaseQuery)
        }
    }
}

// MARK: - News Use Case Error
enum NewsUseCaseError: Error, LocalizedError {
    case emptyNewsId
    case emptyCategoryName
    case emptySearchQuery
    case invalidLimit
    case newsNotFound
    
    var errorDescription: String? {
        switch self {
        case .emptyNewsId:
            return "News ID cannot be empty"
        case .emptyCategoryName:
            return "Category name cannot be empty"
        case .emptySearchQuery:
            return "Search query cannot be empty"
        case .invalidLimit:
            return "Limit must be greater than 0"
        case .newsNotFound:
            return "News item not found"
        }
    }
}
