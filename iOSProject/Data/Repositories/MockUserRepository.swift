import Foundation

// MARK: - Mock User Repository
class MockUserRepository: UserRepositoryProtocol {
    
    // Mock user data
    private let mockUser = User(
        id: "mock_user_123",
        email: "<EMAIL>",
        name: "<PERSON>",
        profileImageURL: nil
    )
    
    // Mock favourites storage
    private var favourites: Set<String> = ["news_1", "news_3"]
    
    func getUser(id: String) async throws -> User {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
        
        if id == mockUser.id {
            return mockUser
        } else {
            throw UserError.userNotFound
        }
    }
    
    func updateUser(_ user: User) async throws -> User {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
        
        // Mock update - return the same user
        return user
    }
    
    func getFavourites() async throws -> [String] {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        
        return Array(favourites)
    }
    
    func addToFavourites(itemId: String) async throws {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        
        favourites.insert(itemId)
    }
    
    func removeFromFavourites(itemId: String) async throws {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        
        favourites.remove(itemId)
    }
}

// MARK: - User Error
enum UserError: Error, LocalizedError {
    case userNotFound
    case updateFailed
    case networkError
    
    var errorDescription: String? {
        switch self {
        case .userNotFound:
            return "User not found"
        case .updateFailed:
            return "Failed to update user"
        case .networkError:
            return "Network connection error"
        }
    }
}
