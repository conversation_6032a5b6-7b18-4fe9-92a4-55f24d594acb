//
//  iOSProjectApp.swift
//  iOSProject
//
//  Created by Apple on 08/08/2025.
//

import SwiftUI

struct ToastView: View {
    var type: ToastStyle
    var title: String
    var message: String
    var onCancelTapped: (() -> Void)

    @Environment(\.appColors) var colors

    var body: some View {

        VStack(alignment: .leading) {
            HStack(alignment: .top) {
                Image(systemName: type.iconFileName)
                    .foregroundColor(type.themeColor)

                VStack(alignment: .leading) {
                    Text(message.localize)
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(colors.primaryText)
                }

                Spacer(minLength: 10)

                Button {
                    onCancelTapped()
                } label: {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(colors.toastCloseButton)
                }
            }
            .padding()
        }
        .background(.ultraThinMaterial)

        .frame(minWidth: 0, maxWidth: .infinity)
        .cornerRadius(32)
        .shadow(color: colors.shadowColor, radius: 4, x: 0, y: 1)
        .padding(.horizontal, 16)
    }
}

struct ToastView_Previews: PreviewProvider {
  static var previews: some View {
    VStack {
        ToastView(
            type: .error,
            title: "Lorem ipsum Lorem ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit amet, consectetur adipiscing elit. ",
            message: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. ") {}

        ToastView(
            type: .info,
            title: "Info",
            message: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. ") {}
    }
    .attachAllEnvironmentObjects()
  }
}



