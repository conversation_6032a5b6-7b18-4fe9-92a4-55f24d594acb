//
//  HomeFeaturesSection.swift
//  iOSProject
//
//  Created by Code Quality Review on 10/08/2025.
//

import SwiftUI

struct HomeFeaturesSection: View {
    @Environment(\.appColors) var colors
    let features: [FeatureItem]

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Section header 
            VStack(alignment: .leading, spacing: 0) {
                Text("Features")
                    .body14SemiBold()
                    .foregroundColor(colors.primaryPurple)

                Spacer().frame(height: 8.h)

                Text(AppConstants.Strings.featuresTitle)
                    .display30SemiBold()
                    .foregroundColor(colors.primaryText)
                    .lineSpacing(4)

                Spacer().frame(height: 16.h)

                Text(AppConstants.Strings.featuresDescription)
                    .headline18Regular()
                    .foregroundColor(colors.secondaryText)
                    .lineSpacing(6)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding(.horizontal, 16.h)

            Spacer().frame(height: 48.h)

            // Features List 
            VStack(spacing: 48.h) {
                ForEach(features, id: \.id) { feature in
                    FeatureItemView(feature: feature)
                        .padding(.horizontal, 16.h)
                }
            }

            // Phone mockup
            GeometryReader { geometry in
                Image(ImageConstants.phoneMockup2)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: geometry.size.width, height: 360.h)
                    .clipped()
            }
            .frame(height: 360.h)
            .frame(maxWidth: .infinity)
            .padding(.top, 32.h)
        }
        .padding(.vertical, 32.h)
    }
}

#Preview {
    HomeFeaturesSection(features: FeatureItem.mockFeatures)
        .attachAllEnvironmentObjects()
}
