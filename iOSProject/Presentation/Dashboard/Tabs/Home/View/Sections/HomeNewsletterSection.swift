//
//  HomeNewsletterSection.swift
//  iOSProject
//
//  Created by Code Quality Review on 10/08/2025.
//

import SwiftUI

struct HomeNewsletterSection: View {
    @Environment(\.appColors) var colors
    @Binding var email: String
    let isSubscribing: Bool
    let onSubscribe: () -> Void
    
    var body: some View {
        VStack(spacing: 24.h) {
            VStack(spacing: 16.h) {
                Text(AppConstants.Strings.stayUpToDate)
                    .display24SemiBold()
                    .foregroundColor(colors.primaryText)
                    .lineSpacing(4)

                Text(AppConstants.Strings.stayUpdatedDescription)
                    .body14Regular()
                    .foregroundColor(colors.secondaryText)
                    .lineSpacing(6)
            }
            
            // Newsletter Signup
            VStack(spacing: 16.h) {
                CustomInputField(
                    text: $email,
                    placeholder: AppConstants.Strings.emailPlaceholder,
                    keyboardType: .emailAddress
                )
                
                CustomButton(
                    title: AppConstants.Strings.subscribe,
                    variant: .filled,
                    isLoading: isSubscribing,
                    action: onSubscribe
                )
            }

            Spacer().frame(height: 64.h)

            // Newsletter Phone Mockup 
            GeometryReader { geometry in
                Image(ImageConstants.phoneMockup4)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: UIScreen.main.bounds.width, height: 360.h)
                    .padding(.horizontal, -16.w)
            }
            .frame(height: 360.h)
            
        }
        .padding(.horizontal, 16.h)
        .padding(.vertical, 48.h)
        .background(colors.surface)
    }
}

#Preview {
    HomeNewsletterSection(
        email: .constant(""),
        isSubscribing: false,
        onSubscribe: {}
    )
    .attachAllEnvironmentObjects()
}
