//
//  HomePricingSection.swift
//  iOSProject
//
//  Created by Code Quality Review on 10/08/2025.
//

import SwiftUI

struct HomePricingSection: View {
    @Environment(\.appColors) var colors
    let pricingPlans: [PricingPlan]
    let onGetStarted: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Section header
            VStack(alignment: .leading, spacing: 0) {
                Text("Upgrade")
                    .body14SemiBold()
                    .foregroundColor(colors.primaryPurple)

                Spacer().frame(height: 8.h)

                Text("Pricing plans that scale with you")
                    .display30SemiBold()
                    .foregroundColor(colors.primaryText)
                    .lineSpacing(4)

                Spacer().frame(height: 24.h)

                Text("Simple, transparent pricing that grows with you. Try any plan free for 30 days.")
                    .headline18Regular()
                    .foregroundColor(colors.secondaryText)
                    .lineSpacing(6)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding(.horizontal, 16.h)

            Spacer().frame(height: 32.h)

            // Pricing Cards 
            VStack(spacing: 32.h) {
                ForEach(pricingPlans, id: \.id) { plan in
                    PricingCardView(plan: plan, onGetStarted: onGetStarted)
                }
            }
            .padding(.horizontal, 16.h)
        }
        .padding(.vertical, 64.h)
    }
}

#Preview {
    HomePricingSection(pricingPlans: PricingPlan.mockPlans, onGetStarted: {})
        .attachAllEnvironmentObjects()
}
