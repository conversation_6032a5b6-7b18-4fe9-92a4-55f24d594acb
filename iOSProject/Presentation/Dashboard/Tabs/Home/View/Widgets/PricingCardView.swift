import SwiftUI

// MARK: - Pricing Card View
struct PricingCardView: View {
    let plan: PricingPlan
    let onGetStarted: () -> Void
    @Environment(\.appColors) var colors
    
    var body: some View {
        ZStack(alignment: .topTrailing) {
            // Main Card 
            VStack(spacing: 0) {
                // Popular badge spacing
                if plan.isPopular {
                    Spacer().frame(height: 20.h)
                }

                // Price
                Text(plan.price)
                    .display36SemiBold()
                    .foregroundColor(colors.primaryText)
                    .lineSpacing(4)

                Spacer().frame(height: 8.h)

                // Title
                Text(plan.title)
                    .headline20SemiBold()
                    .foregroundColor(colors.primaryText)

                Spacer().frame(height: 4.h)

                // Description
                Text(plan.description)
                    .title16Regular()
                    .foregroundColor(Color(hex: "667084"))

                Spacer().frame(height: 24.h)

                // Features List
                VStack(spacing: 0) {
                    ForEach(plan.features, id: \.self) { feature in
                        featureRow(feature)
                            .padding(.bottom, 16.h)
                    }
                }

                Spacer().frame(height: 24.h)

                // Get Started Button
                CustomButton(
                    title: "Get started",
                    action: onGetStarted
                )
            }
            .padding(24.h)
            .background(colors.badgeBackground)
            .overlay(
                RoundedRectangle(cornerRadius: 16.h)
                    .stroke(Color(hex: "EAECF0"), lineWidth: 1)
            )
            .clipShape(RoundedRectangle(cornerRadius: 16.h))
            .shadow(
                color: Color(hex: "0D1018").opacity(0.1),
                radius: 6,
                x: 0,
                y: 4
            )
            .shadow(
                color: Color(hex: "0D1018").opacity(0.08),
                radius: 16,
                x: 0,
                y: 12
            )
            
            // Popular Badge 
            if plan.isPopular {
                popularBadge
                    .offset(y: -32.h)
            }
        }
        .padding(.top, 24.h)
    }
    
    // MARK: - Feature Row 
    private func featureRow(_ feature: String) -> some View {
        HStack(spacing: 12.h) {
            // Check Icon Container 
            RoundedRectangle(cornerRadius: 12.h)
                .fill(Color(hex: "D0FADF"))
                .frame(width: 24.h, height: 24.h)
                .overlay(
                    Image(ImageConstants.imgCheckIcon)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 16.h, height: 16.h)
                )

            // Feature Text
            Text(feature)
                .title16Regular()
                .foregroundColor(Color(hex: "667084"))
                .frame(maxWidth: .infinity, alignment: .leading)
        }
    }
    
    // MARK: - Popular Badge 
    private var popularBadge: some View {
        HStack(alignment: .top, spacing: 8.h) {
            // Custom Badge Icon 
            Image(ImageConstants.imgVectors)
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 56.h, height: 56.h)
                .padding(.top, 8.h)

            // "Most popular!" text
            Text("Most popular!")
                .body14Medium()
                .foregroundColor(Color(hex: "6840C6"))
        }
        .frame(maxWidth: .infinity, alignment: .trailing)
    }
}

// MARK: - Preview
struct PricingCardView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 32) {
            PricingCardView(
                plan: PricingPlan(
                    price: "$10/mth",
                    title: "Basic plan",
                    description: "Billed annually.",
                    isPopular: true,
                    features: [
                        "Access to all basic features",
                        "Basic reporting and analytics",
                        "Up to 10 individual users"
                    ]
                ),
                onGetStarted: { }
            )
            
//            PricingCardView(
//                plan: PricingPlan(
//                    price: "$20/mth",
//                    title: "Business plan",
//                    description: "Billed annually.",
//                    isPopular: false,
//                    features: [
//                        "200+ integrations",
//                        "Advanced reporting",
//                        "Up to 20 individual users"
//                    ]
//                ),
//                onGetStarted: { }
//            )
        }
        .padding()
        .attachAllEnvironmentObjects()
    }
}
