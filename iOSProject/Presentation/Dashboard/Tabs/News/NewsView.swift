import SwiftUI

// MARK: - News View

struct NewsView: View {
    @Environment(\.appColors) var colors

    var body: some View {
        GeometryReader { geo in
            let size = geo.size
            MainScrollBody {
                VStack(spacing: 0) {
                    // a placeholder view

                    Text("News View")
                        .font(.headline)
                        .foregroundColor(colors.secondaryText)
                        .padding()
                }
                .frame(width: size.width, height: size.height, alignment: .center)
                .background(colors.background)
            }
        }
    }
}

// MARK: - Preview

#Preview {
    NewsView()
}
