/// Authentication repository interface
/// 
/// Defines the contract for authentication data operations.
/// This follows clean architecture principles by separating the domain
/// layer (interface) from the data layer (implementation).
abstract class AuthRepository {
  /// Authenticates user with email and password
  /// 
  /// Returns true if authentication is successful, false otherwise
  Future<bool> authenticateWithCredentials(String email, String password);
  
  /// Authenticates user with Google
  /// 
  /// Returns true if authentication is successful, false otherwise
  Future<bool> authenticateWithGoogle();
  
  /// Authenticates user with Apple
  /// 
  /// Returns true if authentication is successful, false otherwise
  Future<bool> authenticateWithApple();
  
  /// Sends password reset email
  /// 
  /// Returns true if email was sent successfully, false otherwise
  Future<bool> sendPasswordResetEmail(String email);
  
  /// Logs out the current user
  /// 
  /// Returns true if logout was successful, false otherwise
  Future<bool> logout();
}

/// Mock implementation of AuthRepository for development/testing
/// 
/// This provides a concrete implementation that simulates authentication
/// without requiring actual backend services.
class MockAuthRepository implements AuthRepository {
  @override
  Future<bool> authenticateWithCredentials(String email, String password) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 2700));
    
    // For demo purposes, always return true
    // In a real implementation, this would make API calls
    return true;
  }
  
  @override
  Future<bool> authenticateWithGoogle() async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 1500));
    return true;
  }
  
  @override
  Future<bool> authenticateWithApple() async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 1500));
    return true;
  }
  
  @override
  Future<bool> sendPasswordResetEmail(String email) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 1000));
    return true;
  }
  
  @override
  Future<bool> logout() async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    return true;
  }
}
