/// Authentication validation service
/// 
/// Provides business logic for validating authentication inputs.
/// This service layer separates validation logic from the presentation layer,
/// following clean architecture principles.
class AuthValidationService {
  // Email validation regex pattern
  static const String _emailPattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  
  /// Validates email format and requirements
  /// 
  /// Returns null if valid, error message if invalid
  static String? validateEmail(String email) {
    if (email.isEmpty) return 'Email is required';
    if (!RegExp(_emailPattern).hasMatch(email)) return 'Please enter a valid email address';
    return null;
  }
  
  /// Validates password format and requirements
  /// 
  /// Returns null if valid, error message if invalid
  static String? validatePassword(String password) {
    if (password.isEmpty) return 'Password is required';
    if (password.length < 6) return 'Password must be at least 6 characters';
    return null;
  }
  
  /// Validates both email and password
  /// 
  /// Returns a map with 'email' and 'password' keys containing error messages,
  /// or null values if validation passes
  static Map<String, String?> validateCredentials(String email, String password) {
    return {
      'email': validateEmail(email),
      'password': validatePassword(password),
    };
  }
  
  /// Checks if credentials are valid
  /// 
  /// Returns true if both email and password pass validation
  static bool areCredentialsValid(String email, String password) {
    final validation = validateCredentials(email, password);
    return validation['email'] == null && validation['password'] == null;
  }
  
  // Private constructor to prevent instantiation
  AuthValidationService._();
}
