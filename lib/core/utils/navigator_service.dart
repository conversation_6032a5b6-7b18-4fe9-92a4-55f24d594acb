import 'package:flutter/material.dart';

/// Custom exception for navigation errors
class NavigationException implements Exception {
  final String message;
  const NavigationException(this.message);

  @override
  String toString() => 'NavigationException: $message';
}

// ignore_for_file: must_be_immutable
class NavigatorService {
  static GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  /// Navigate to a named route with error handling
  static Future<dynamic> pushNamed(String routeName, {dynamic arguments}) async {
    try {
      final navigator = navigatorKey.currentState;
      if (navigator == null) {
        throw const NavigationException('Navigator not available');
      }

      if (routeName.isEmpty) {
        throw const NavigationException('Route name cannot be empty');
      }

      return await navigator.pushNamed(routeName, arguments: arguments);
    } catch (e) {
      debugPrint('Navigation failed for route "$routeName": $e');
      rethrow;
    }
  }

  /// Go back to previous screen with error handling
  static void goBack() {
    try {
      final navigator = navigatorKey.currentState;
      if (navigator == null) {
        throw const NavigationException('Navigator not available');
      }

      if (!navigator.canPop()) {
        debugPrint('Cannot go back: No previous route available');
        return;
      }

      navigator.pop();
    } catch (e) {
      debugPrint('Failed to go back: $e');
      // Don't rethrow for goBack as it's often called from UI events
    }
  }

  /// Navigate to a named route and remove all previous routes with error handling
  static Future<dynamic> pushNamedAndRemoveUntil(String routeName, {bool routePredicate = false, dynamic arguments}) async {
    try {
      final navigator = navigatorKey.currentState;
      if (navigator == null) {
        throw const NavigationException('Navigator not available');
      }

      if (routeName.isEmpty) {
        throw const NavigationException('Route name cannot be empty');
      }

      return await navigator.pushNamedAndRemoveUntil(routeName, (route) => routePredicate, arguments: arguments);
    } catch (e) {
      debugPrint('Navigation failed for pushNamedAndRemoveUntil "$routeName": $e');
      rethrow;
    }
  }

  /// Pop current route and push a new named route with error handling
  static Future<dynamic> popAndPushNamed(String routeName, {dynamic arguments}) async {
    try {
      final navigator = navigatorKey.currentState;
      if (navigator == null) {
        throw const NavigationException('Navigator not available');
      }

      if (routeName.isEmpty) {
        throw const NavigationException('Route name cannot be empty');
      }

      return await navigator.popAndPushNamed(routeName, arguments: arguments);
    } catch (e) {
      debugPrint('Navigation failed for popAndPushNamed "$routeName": $e');
      rethrow;
    }
  }
}
