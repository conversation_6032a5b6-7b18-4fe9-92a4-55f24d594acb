/// Semantic dimension constants for the application
/// Replaces magic numbers throughout the codebase
class AppDimensions {
  // AppBar dimensions
  static const double appBarLeadingIconSize = 32.0;
  static const double appBarLeadingPadding = 16.0;
  static const double appBarLeadingWidth = 52.0;
  static const double appBarLeadingExtraPadding = 24.0;
  static const double appBarMenuIconSize = 24.0;
  static const double appBarActionPadding = 8.0;
  
  // Button dimensions
  static const double buttonDefaultHeight = 44.0;
  static const double buttonIconSize = 20.0;
  static const double buttonIconSpacing = 12.0;
  static const double buttonBorderRadius = 8.0;
  static const double buttonElevation = 1.0;
  static const double buttonBorderWidth = 1.0;
  static const double buttonHorizontalPadding = 16.0;
  
  // Input field dimensions
  static const double inputFieldDefaultHeight = 48.0;
  static const double inputFieldVerticalPadding = 12.0;
  static const double inputFieldBorderRadius = 8.0;
  static const double inputFieldBorderWidth = 1.0;
  static const double inputFieldPrefixIconSize = 32.0;
  static const double inputFieldHorizontalPadding = 16.0;
  static const int inputFieldDefaultMaxLines = 1;
  
  // Common UI dimensions
  static const int snackBarDurationSeconds = 2;
  
  // Private constructor to prevent instantiation
  AppDimensions._();
}
