import '../../presentation/landing_screen/models/feature_item_model.dart';
import '../../presentation/landing_screen/models/pricing_plan_model.dart';
import 'image_constant.dart';

/// Default landing page data constants for the application
/// Centralizes hardcoded feature and pricing data for better maintainability
class DefaultLandingData {
  
  /// Default feature items for the landing page
  static List<FeatureItemModel> getDefaultFeatures() {
    return [
      FeatureItemModel(
        icon: ImageConstant.imgPiechart,
        title: 'Access to daily analytics',
        description:
            'Optimize the way you recover, train, and sleep with daily reporting on mobile and desktop apps. Start training smarter, not harder.',
      ),
      FeatureItemModel(
        icon: ImageConstant.imgZap,
        title: 'Measure recovery',
        description:
            'The most advanced sleep tracking technology available today. Measure and track your recovery to unlock your greatest potential.',
      ),
      FeatureItemModel(
        icon: ImageConstant.imgSmartphone,
        title: 'Tech that evolves with you',
        description:
            'Know where your strengths lie and where you can improve. Untitled provides the latest tech with a steady stream of new features.',
      ),
      FeatureItemModel(
        icon: ImageConstant.imgUsers,
        title: 'Unrivalled community',
        description:
            'Join teams in the app with friends, family, and like-minded fitness enthusiasts. Create custom teams based on activities and interests.',
      ),
    ];
  }

  /// Default pricing plans for the landing page
  static List<PricingPlanModel> getDefaultPricingPlans() {
    return [
      PricingPlanModel(
        price: '\$10/mth',
        title: 'Basic plan',
        description: 'Billed annually.',
        isPopular: true,
        features: [
          'Access to all basic features',
          'Basic reporting and analytics',
          'Up to 10 individual users',
          '20GB individual data each user',
          'Basic chat and email support',
        ],
      ),
      PricingPlanModel(
        price: '\$20/mth',
        title: 'Business plan',
        description: 'Billed annually.',
        isPopular: false,
        features: [
          '200+ integrations',
          'Advanced reporting',
          'Up to 20 individual users',
          '40GB individual data each user',
          'Priority chat and email support',
        ],
      ),
    ];
  }
  
  // Private constructor to prevent instantiation
  DefaultLandingData._();
}
