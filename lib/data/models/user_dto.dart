import '../../domain/entities/user_entity.dart';

/// Data Transfer Object for user data.
/// 
/// This class handles data serialization/deserialization between
/// the data layer and external sources (APIs, databases, local storage).
/// It follows Clean Architecture principles by separating data concerns
/// from domain entities.
/// 
/// The DTO is responsible for:
/// - JSON serialization/deserialization
/// - Data validation and transformation
/// - Mapping between external data formats and domain entities
class UserDto {
  final String id;
  final String name;
  final String email;
  final String? profileImageUrl;

  UserDto({
    required this.id,
    required this.name,
    required this.email,
    this.profileImageUrl,
  });

  /// Creates a UserDto from JSON data.
  /// 
  /// Used when deserializing data from APIs or local storage.
  /// <PERSON>les type casting and provides default values for missing fields.
  factory UserDto.fromJson(Map<String, dynamic> json) {
    return UserDto(
      id: json['id'] as String? ?? '',
      name: json['name'] as String? ?? '',
      email: json['email'] as String? ?? '',
      profileImageUrl: json['profile_image_url'] as String?,
    );
  }

  /// Converts UserDto to JSON format.
  /// 
  /// Used when serializing data for APIs or local storage.
  /// Ensures consistent data format across different storage mechanisms.
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'profile_image_url': profileImageUrl,
    };
  }

  /// Converts UserDto to domain entity.
  /// 
  /// This is the key mapping function that transforms data layer objects
  /// into domain layer entities. It ensures that domain entities remain
  /// independent of external data formats.
  UserEntity toEntity() {
    return UserEntity(
      id: id,
      name: name,
      email: email,
      profileImageUrl: profileImageUrl,
    );
  }

  /// Creates UserDto from domain entity.
  /// 
  /// Used when converting domain entities back to data layer format
  /// for storage or API communication.
  factory UserDto.fromEntity(UserEntity entity) {
    return UserDto(
      id: entity.id,
      name: entity.name,
      email: entity.email,
      profileImageUrl: entity.profileImageUrl,
    );
  }

  /// Creates a copy of this UserDto with updated fields.
  /// 
  /// Useful for data transformations and updates without
  /// modifying the original object.
  UserDto copyWith({
    String? id,
    String? name,
    String? email,
    String? profileImageUrl,
  }) {
    return UserDto(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserDto &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          name == other.name &&
          email == other.email &&
          profileImageUrl == other.profileImageUrl;

  @override
  int get hashCode =>
      id.hashCode ^
      name.hashCode ^
      email.hashCode ^
      profileImageUrl.hashCode;

  @override
  String toString() {
    return 'UserDto{id: $id, name: $name, email: $email, profileImageUrl: $profileImageUrl}';
  }
}
