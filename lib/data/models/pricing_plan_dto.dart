import 'package:collection/collection.dart';
import '../../domain/entities/pricing_plan_entity.dart';

/// Data Transfer Object for pricing plan data.
/// 
/// This class handles data serialization/deserialization between
/// the data layer and external sources (APIs, databases, local storage).
/// It follows Clean Architecture principles by separating data concerns
/// from domain entities.
/// 
/// The DTO is responsible for:
/// - JSON serialization/deserialization
/// - Data validation and transformation
/// - Mapping between external data formats and domain entities
class PricingPlanDto {
  final String id;
  final String price;
  final String title;
  final String description;
  final bool isPopular;
  final List<String> features;

  PricingPlanDto({
    required this.id,
    required this.price,
    required this.title,
    required this.description,
    required this.isPopular,
    required this.features,
  });

  /// Creates a PricingPlanDto from JSON data.
  /// 
  /// Used when deserializing data from APIs or local storage.
  /// Handles type casting and provides default values for missing fields.
  factory PricingPlanDto.fromJson(Map<String, dynamic> json) {
    return PricingPlanDto(
      id: json['id'] as String? ?? '',
      price: json['price'] as String? ?? '',
      title: json['title'] as String? ?? '',
      description: json['description'] as String? ?? '',
      isPopular: json['is_popular'] as bool? ?? false,
      features: (json['features'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ?? [],
    );
  }

  /// Converts PricingPlanDto to JSON format.
  /// 
  /// Used when serializing data for APIs or local storage.
  /// Ensures consistent data format across different storage mechanisms.
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'price': price,
      'title': title,
      'description': description,
      'is_popular': isPopular,
      'features': features,
    };
  }

  /// Converts PricingPlanDto to domain entity.
  /// 
  /// This is the key mapping function that transforms data layer objects
  /// into domain layer entities. It ensures that domain entities remain
  /// independent of external data formats.
  PricingPlanEntity toEntity() {
    return PricingPlanEntity(
      id: id,
      price: price,
      title: title,
      description: description,
      isPopular: isPopular,
      features: List<String>.from(features),
    );
  }

  /// Creates PricingPlanDto from domain entity.
  /// 
  /// Used when converting domain entities back to data layer format
  /// for storage or API communication.
  factory PricingPlanDto.fromEntity(PricingPlanEntity entity) {
    return PricingPlanDto(
      id: entity.id,
      price: entity.price,
      title: entity.title,
      description: entity.description,
      isPopular: entity.isPopular,
      features: List<String>.from(entity.features),
    );
  }

  /// Creates a copy of this PricingPlanDto with updated fields.
  /// 
  /// Useful for data transformations and updates without
  /// modifying the original object.
  PricingPlanDto copyWith({
    String? id,
    String? price,
    String? title,
    String? description,
    bool? isPopular,
    List<String>? features,
  }) {
    return PricingPlanDto(
      id: id ?? this.id,
      price: price ?? this.price,
      title: title ?? this.title,
      description: description ?? this.description,
      isPopular: isPopular ?? this.isPopular,
      features: features ?? this.features,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PricingPlanDto &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          price == other.price &&
          title == other.title &&
          description == other.description &&
          isPopular == other.isPopular &&
          const ListEquality().equals(features, other.features);

  @override
  int get hashCode =>
      id.hashCode ^
      price.hashCode ^
      title.hashCode ^
      description.hashCode ^
      isPopular.hashCode ^
      const ListEquality().hash(features);

  @override
  String toString() {
    return 'PricingPlanDto{id: $id, price: $price, title: $title, description: $description, isPopular: $isPopular, features: $features}';
  }
}
