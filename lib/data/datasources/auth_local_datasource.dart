import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_dto.dart';

/// Local data source interface for authentication operations.
/// 
/// This interface defines the contract for local authentication data operations
/// following Clean Architecture principles. It abstracts the details of local
/// storage mechanisms from the repository layer.
/// 
/// The local data source is responsible for caching user data,
/// storing authentication tokens, and managing offline capabilities.
abstract class AuthLocalDataSource {
  /// Caches user data locally for offline access.
  /// 
  /// Stores [UserDto] in local storage for quick retrieval.
  /// Throws exceptions on storage failures.
  Future<void> cacheUser(UserDto user);

  /// Retrieves cached user data from local storage.
  /// 
  /// Returns [UserDto] if cached data exists, null otherwise.
  /// Throws exceptions on storage access failures.
  Future<UserDto?> getCachedUser();

  /// Clears all cached authentication data.
  /// 
  /// Removes user data, tokens, and other auth-related cache.
  /// Used during logout or when cache becomes invalid.
  Future<void> clearCache();

  /// Stores authentication token locally.
  /// 
  /// Saves authentication token for API requests.
  /// Throws exceptions on storage failures.
  Future<void> storeAuthToken(String token);

  /// Retrieves stored authentication token.
  /// 
  /// Returns token string if exists, null otherwise.
  /// Used for authenticated API requests.
  Future<String?> getAuthToken();

  /// Clears stored authentication token.
  /// 
  /// Removes token from local storage.
  /// Used during logout or token expiration.
  Future<void> clearAuthToken();
}

/// Implementation of the local authentication data source.
/// 
/// This class handles local storage operations using SharedPreferences.
/// It provides caching capabilities for user data and authentication tokens
/// to support offline functionality and improve app performance.
/// 
/// In a production environment, this might also use secure storage
/// for sensitive data like authentication tokens.
class AuthLocalDataSourceImpl implements AuthLocalDataSource {
  static const String _userKey = 'cached_user';
  static const String _tokenKey = 'auth_token';

  @override
  Future<void> cacheUser(UserDto user) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = jsonEncode(user.toJson());
      await prefs.setString(_userKey, userJson);
    } catch (e) {
      throw Exception('Failed to cache user data: $e');
    }
  }

  @override
  Future<UserDto?> getCachedUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString(_userKey);
      
      if (userJson != null) {
        final userMap = jsonDecode(userJson) as Map<String, dynamic>;
        return UserDto.fromJson(userMap);
      }
      
      return null;
    } catch (e) {
      // Log error but don't throw - return null for graceful degradation
      print('Failed to get cached user: $e');
      return null;
    }
  }

  @override
  Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_userKey);
    } catch (e) {
      throw Exception('Failed to clear user cache: $e');
    }
  }

  @override
  Future<void> storeAuthToken(String token) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_tokenKey, token);
    } catch (e) {
      throw Exception('Failed to store auth token: $e');
    }
  }

  @override
  Future<String?> getAuthToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_tokenKey);
    } catch (e) {
      // Log error but don't throw - return null for graceful degradation
      print('Failed to get auth token: $e');
      return null;
    }
  }

  @override
  Future<void> clearAuthToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_tokenKey);
    } catch (e) {
      throw Exception('Failed to clear auth token: $e');
    }
  }
}
