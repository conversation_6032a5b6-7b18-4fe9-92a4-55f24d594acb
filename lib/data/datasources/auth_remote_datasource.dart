import '../models/user_dto.dart';

/// Remote data source interface for authentication operations.
///
/// This interface defines the contract for remote authentication data operations
/// following Clean Architecture principles. It abstracts the details of external
/// APIs and services from the repository layer.
///
/// The data source is responsible for handling network communication,
/// API calls, and data serialization/deserialization.
abstract class AuthRemoteDataSource {
  /// Authenticates user with email and password via remote API.
  ///
  /// Returns [UserDto] on successful authentication.
  /// Throws exceptions on failure (network, server, authentication errors).
  Future<UserDto> login(String email, String password);

  /// Authenticates user with Google OAuth via remote API.
  ///
  /// Returns [UserDto] on successful authentication.
  /// Throws exceptions on failure (network, server, authentication errors).
  Future<UserDto> loginWithGoogle();

  /// Authenticates user with Apple Sign-In via remote API.
  ///
  /// Returns [UserDto] on successful authentication.
  /// Throws exceptions on failure (network, server, authentication errors).
  Future<UserDto> loginWithApple();

  /// Sends password reset email via remote API.
  ///
  /// Returns [true] if email was sent successfully.
  /// Throws exceptions on failure (network, server errors).
  Future<bool> sendPasswordResetEmail(String email);

  /// Logs out user via remote API.
  ///
  /// Returns [true] if logout was successful.
  /// Throws exceptions on failure (network, server errors).
  Future<bool> logout();
}

/// Implementation of the remote authentication data source.
///
/// This class handles actual network communication with authentication APIs.
/// It simulates real API calls with realistic delays and responses for
/// development and testing purposes.
///
/// In a production environment, this would make actual HTTP requests
/// to authentication endpoints using packages like dio or http.
class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  @override
  Future<UserDto> login(String email, String password) async {
    // Simulate API call with realistic delay
    await Future.delayed(const Duration(milliseconds: 2700));

    // Simulate successful authentication response
    // In production, this would parse actual API response
    return UserDto(
      id: 'user_${DateTime.now().millisecondsSinceEpoch}',
      name: 'John Doe',
      email: email,
      profileImageUrl: 'https://example.com/avatar.jpg',
    );
  }

  @override
  Future<UserDto> loginWithGoogle() async {
    // Simulate Google OAuth API call
    await Future.delayed(Duration(milliseconds: 1500));

    return UserDto(
      id: 'google_user_${DateTime.now().millisecondsSinceEpoch}',
      name: 'Google User',
      email: '<EMAIL>',
      profileImageUrl: 'https://lh3.googleusercontent.com/avatar.jpg',
    );
  }

  @override
  Future<UserDto> loginWithApple() async {
    // Simulate Apple Sign-In API call
    await Future.delayed(Duration(milliseconds: 1500));

    return UserDto(
      id: 'apple_user_${DateTime.now().millisecondsSinceEpoch}',
      name: 'Apple User',
      email: '<EMAIL>',
      profileImageUrl: null, // Apple often doesn't provide profile images
    );
  }

  @override
  Future<bool> sendPasswordResetEmail(String email) async {
    // Simulate password reset API call
    await Future.delayed(Duration(milliseconds: 1000));

    // In production, this would make actual API call
    // and return based on server response
    return true;
  }

  @override
  Future<bool> logout() async {
    // Simulate logout API call
    await Future.delayed(Duration(milliseconds: 500));

    // In production, this would invalidate tokens on server
    return true;
  }
}
