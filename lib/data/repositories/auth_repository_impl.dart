import 'package:dartz/dartz.dart';
import '../../domain/entities/user_entity.dart';
import '../../domain/failures/auth_failure.dart';
import '../../domain/repositories/auth_repository.dart';
import '../datasources/auth_remote_datasource.dart';
import '../datasources/auth_local_datasource.dart';

/// Implementation of the authentication repository.
/// 
/// This class implements the domain repository interface and coordinates
/// between remote and local data sources. It follows Clean Architecture
/// principles by implementing the domain contract while handling data
/// layer concerns like caching, error handling, and data transformation.
/// 
/// The repository is responsible for:
/// - Coordinating between remote and local data sources
/// - Handling network failures and providing fallbacks
/// - Caching data for offline access
/// - Converting data layer objects to domain entities
/// - Converting exceptions to domain failures
class AuthRepositoryImpl implements AuthRepository {
  final AuthRemoteDataSource _remoteDataSource;
  final AuthLocalDataSource _localDataSource;

  AuthRepositoryImpl(this._remoteDataSource, this._localDataSource);

  @override
  Future<Either<AuthFailure, UserEntity>> authenticateWithCredentials(
    String email,
    String password,
  ) async {
    try {
      // Attempt remote authentication
      final userDto = await _remoteDataSource.login(email, password);
      
      // Cache user data locally for offline access
      await _localDataSource.cacheUser(userDto);
      
      // Convert DTO to domain entity
      return Right(userDto.toEntity());
    } catch (e) {
      // Convert exceptions to domain failures
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<AuthFailure, UserEntity>> authenticateWithGoogle() async {
    try {
      // Attempt Google authentication
      final userDto = await _remoteDataSource.loginWithGoogle();
      
      // Cache user data locally
      await _localDataSource.cacheUser(userDto);
      
      // Convert DTO to domain entity
      return Right(userDto.toEntity());
    } catch (e) {
      // Convert exceptions to domain failures
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<AuthFailure, UserEntity>> authenticateWithApple() async {
    try {
      // Attempt Apple authentication
      final userDto = await _remoteDataSource.loginWithApple();
      
      // Cache user data locally
      await _localDataSource.cacheUser(userDto);
      
      // Convert DTO to domain entity
      return Right(userDto.toEntity());
    } catch (e) {
      // Convert exceptions to domain failures
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<AuthFailure, bool>> sendPasswordResetEmail(String email) async {
    try {
      // Attempt to send password reset email
      final result = await _remoteDataSource.sendPasswordResetEmail(email);
      return Right(result);
    } catch (e) {
      // Convert exceptions to domain failures
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<AuthFailure, bool>> logout() async {
    try {
      // Attempt remote logout
      await _remoteDataSource.logout();
      
      // Clear local cache
      await _localDataSource.clearCache();
      await _localDataSource.clearAuthToken();
      
      return Right(true);
    } catch (e) {
      // Even if remote logout fails, clear local data
      try {
        await _localDataSource.clearCache();
        await _localDataSource.clearAuthToken();
      } catch (_) {
        // Ignore local clear failures during logout
      }
      
      // Convert exceptions to domain failures
      return Left(_mapExceptionToFailure(e));
    }
  }

  /// Maps data layer exceptions to domain failures.
  /// 
  /// This method centralizes the conversion of various exceptions
  /// into appropriate domain failure types, maintaining the separation
  /// between data layer concerns and domain layer abstractions.
  AuthFailure _mapExceptionToFailure(dynamic exception) {
    final errorMessage = exception.toString().toLowerCase();
    
    // Network-related errors
    if (errorMessage.contains('network') || 
        errorMessage.contains('connection') ||
        errorMessage.contains('timeout')) {
      return NetworkFailure();
    }
    
    // Server-related errors
    if (errorMessage.contains('server') || 
        errorMessage.contains('500') ||
        errorMessage.contains('502') ||
        errorMessage.contains('503')) {
      return ServerFailure();
    }
    
    // Authentication-related errors
    if (errorMessage.contains('unauthorized') || 
        errorMessage.contains('401') ||
        errorMessage.contains('invalid credentials') ||
        errorMessage.contains('authentication failed')) {
      return AuthenticationFailure();
    }
    
    // Default to unknown failure
    return UnknownFailure(exception.toString());
  }
}
