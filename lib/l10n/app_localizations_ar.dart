// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get back => 'رجوع';

  @override
  String get darkMode => 'الوضع المظلم';

  @override
  String get editProfile => 'تعديل الملف الشخصي';

  @override
  String get address => 'العنوان';

  @override
  String get history => 'التاريخ';

  @override
  String get complain => 'شكوى';

  @override
  String get referral => 'الإحالة';

  @override
  String get aboutUs => 'معلومات عنا';

  @override
  String get settings => 'الإعدادات';

  @override
  String get helpAndSupport => 'المساعدة والدعم';

  @override
  String get logout => 'تسجيل الخروج';

  @override
  String get selectLanguage => 'اختر اللغة';

  @override
  String get english => 'English';

  @override
  String get arabic => 'العربية';

  @override
  String get portfolioPerformance => 'تتبع أداء المحفظة أصبح سهلاً';

  @override
  String get portfolioDescription =>
      'تحليلات قوية للمنتج والنمو لمساعدتك في التحويل والمشاركة والاحتفاظ بمزيد من المستخدمين. موثوق به من قبل أكثر من 4000 شركة ناشئة.';

  @override
  String get appStore => 'متجر التطبيقات';

  @override
  String get playStore => 'متجر بلاي';

  @override
  String get language => 'اللغة';

  @override
  String get newFeature => 'جديد';

  @override
  String get startFreeTrial => 'ابدأ تجربتك المجانية';

  @override
  String get unlockYourself => 'اكتشف إمكانياتك';

  @override
  String get getStarted => 'ابدأ الآن';

  @override
  String get logIn => 'تسجيل الدخول';

  @override
  String get signUp => 'إنشاء حساب';

  @override
  String get analyticsFromFuture => 'تحليلات تبدو وكأنها من المستقبل';

  @override
  String get join4000Companies => 'انضم إلى أكثر من 4000 شركة تنمو بالفعل';
}
