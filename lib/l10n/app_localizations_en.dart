// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get back => 'Back';

  @override
  String get darkMode => 'Dark Mode';

  @override
  String get editProfile => 'Edit Profile';

  @override
  String get address => 'Address';

  @override
  String get history => 'History';

  @override
  String get complain => 'Complain';

  @override
  String get referral => 'Referral';

  @override
  String get aboutUs => 'About Us';

  @override
  String get settings => 'Settings';

  @override
  String get helpAndSupport => 'Help and Support';

  @override
  String get logout => 'Logout';

  @override
  String get selectLanguage => 'Select Language';

  @override
  String get english => 'English';

  @override
  String get arabic => 'العربية';

  @override
  String get portfolioPerformance => 'Portfolio performance tracking made easy';

  @override
  String get portfolioDescription =>
      'Powerful, self-serve product and growth analytics to help you convert, engage, and retain more users. Trusted by over 4,000 startups.';

  @override
  String get appStore => 'App Store';

  @override
  String get playStore => 'Play Store';

  @override
  String get language => 'Language';

  @override
  String get newFeature => 'New';

  @override
  String get startFreeTrial => 'Start your free trial';

  @override
  String get unlockYourself => 'Unlock yourself';

  @override
  String get getStarted => 'Get started';

  @override
  String get logIn => 'Log in';

  @override
  String get signUp => 'Sign up';

  @override
  String get analyticsFromFuture =>
      'Analytics that feels like it\'s from the future';

  @override
  String get join4000Companies => 'Join 4,000+ companies already growing';
}
