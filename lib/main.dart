import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_project/presentation/app/my_app.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'core/di/injection_container.dart' as di;
import 'presentation/app/error_app.dart';

void main() async {
  try {
    WidgetsFlutterBinding.ensureInitialized();

    // Initialize dependency injection
    await di.init();

    final prefs = await SharedPreferences.getInstance();

    await Future.wait([
      SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]),
    ]);

    runApp(MyApp(prefs: prefs));
  } catch (e) {
    // Handle initialization errors gracefully
    runApp(ErrorApp(error: e.toString()));
  }
}
