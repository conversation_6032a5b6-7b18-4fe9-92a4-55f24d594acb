import 'package:dartz/dartz.dart';
import '../entities/feature_entity.dart';
import '../entities/pricing_plan_entity.dart';
import '../failures/landing_failure.dart';

/// Repository interface for landing page operations in the domain layer.
/// 
/// This interface defines the contract for landing page data operations
/// following Clean Architecture principles. It's independent of any specific
/// implementation details and can be implemented by different data sources.
/// 
/// The repository pattern provides abstraction between the domain layer
/// and external concerns like APIs, databases, or local storage.
abstract class LandingRepository {
  /// Retrieves the list of app features for display on the landing page.
  /// 
  /// Returns [Right] with [List<FeatureEntity>] on successful data retrieval,
  /// or [Left] with [LandingFailure] on failure.
  /// 
  /// Possible failures:
  /// - [DataLoadFailure] if features cannot be loaded
  /// - [NetworkFailure] if network connectivity issues occur
  /// - [ServerFailure] if server returns unexpected error
  /// - [UnknownFailure] for any other unexpected errors
  Future<Either<LandingFailure, List<FeatureEntity>>> getFeatures();

  /// Retrieves the list of pricing plans for display on the landing page.
  /// 
  /// Returns [Right] with [List<PricingPlanEntity>] on successful data retrieval,
  /// or [Left] with [LandingFailure] on failure.
  /// 
  /// Possible failures:
  /// - [DataLoadFailure] if pricing plans cannot be loaded
  /// - [NetworkFailure] if network connectivity issues occur
  /// - [ServerFailure] if server returns unexpected error
  /// - [UnknownFailure] for any other unexpected errors
  Future<Either<LandingFailure, List<PricingPlanEntity>>> getPricingPlans();

  /// Subscribes an email address to the newsletter.
  /// 
  /// Returns [Right] with [true] if subscription was successful,
  /// or [Left] with [LandingFailure] on failure.
  /// 
  /// Possible failures:
  /// - [SubscriptionFailure] if subscription cannot be processed
  /// - [ValidationFailure] if email format is invalid
  /// - [NetworkFailure] if network connectivity issues occur
  /// - [ServerFailure] if server returns unexpected error
  /// - [UnknownFailure] for any other unexpected errors
  Future<Either<LandingFailure, bool>> subscribeToNewsletter(String email);
}
