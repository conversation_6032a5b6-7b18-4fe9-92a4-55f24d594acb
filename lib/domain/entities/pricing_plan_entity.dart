import 'package:collection/collection.dart';

/// Domain entity representing a pricing plan in the business logic layer.
/// 
/// This entity encapsulates the core pricing plan data and business rules,
/// independent of any external concerns like UI or data storage.
/// Used in the landing page to display subscription options.
class PricingPlanEntity {
  final String id;
  final String price;
  final String title;
  final String description;
  final bool isPopular;
  final List<String> features;

  const PricingPlanEntity({
    required this.id,
    required this.price,
    required this.title,
    required this.description,
    required this.isPopular,
    required this.features,
  });

  /// Creates a copy of this pricing plan entity with updated fields
  PricingPlanEntity copyWith({
    String? id,
    String? price,
    String? title,
    String? description,
    bool? isPopular,
    List<String>? features,
  }) {
    return PricingPlanEntity(
      id: id ?? this.id,
      price: price ?? this.price,
      title: title ?? this.title,
      description: description ?? this.description,
      isPopular: isPopular ?? this.isPopular,
      features: features ?? this.features,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PricingPlanEntity &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          price == other.price &&
          title == other.title &&
          description == other.description &&
          isPopular == other.isPopular &&
          const ListEquality().equals(features, other.features);

  @override
  int get hashCode =>
      id.hashCode ^
      price.hashCode ^
      title.hashCode ^
      description.hashCode ^
      isPopular.hashCode ^
      const ListEquality().hash(features);

  @override
  String toString() {
    return 'PricingPlanEntity{id: $id, price: $price, title: $title, description: $description, isPopular: $isPopular, features: $features}';
  }
}
