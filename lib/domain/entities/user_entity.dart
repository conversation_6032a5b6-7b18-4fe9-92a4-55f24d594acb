/// Domain entity representing a user in the business logic layer.
///
/// This entity encapsulates the core user data and business rules,
/// independent of any external concerns like UI or data storage.
/// It follows Clean Architecture principles by being framework-agnostic.
class UserEntity {
  final String id;
  final String name;
  final String email;
  final String? profileImageUrl;

  const UserEntity({required this.id, required this.name, required this.email, this.profileImageUrl});

  /// Creates a copy of this user entity with updated fields
  UserEntity copyWith({String? id, String? name, String? email, String? profileImageUrl}) {
    return UserEntity(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserEntity &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          name == other.name &&
          email == other.email &&
          profileImageUrl == other.profileImageUrl;

  @override
  int get hashCode => id.hashCode ^ name.hashCode ^ email.hashCode ^ profileImageUrl.hashCode;

  @override
  String toString() {
    return 'UserEntity{id: $id, name: $name, email: $email, profileImageUrl: $profileImageUrl}';
  }
}
