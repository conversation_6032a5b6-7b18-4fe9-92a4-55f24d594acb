import 'package:dartz/dartz.dart';
import '../../entities/user_entity.dart';
import '../../failures/auth_failure.dart';
import '../../repositories/auth_repository.dart';

/// Use case interface for Apple login operations.
/// 
/// This interface defines the contract for Apple ID authentication business logic
/// following Clean Architecture principles. It encapsulates the business rules
/// for Apple Sign-In authentication.
abstract class AppleLoginUseCase {
  /// Executes the Apple login use case.
  /// 
  /// Returns [Right] with [UserEntity] on successful authentication,
  /// or [Left] with [AuthFailure] on failure.
  Future<Either<AuthFailure, UserEntity>> call();
}

/// Implementation of the Apple login use case.
/// 
/// This class encapsulates the business logic for Apple Sign-In authentication.
/// It follows Clean Architecture principles by being independent of external
/// frameworks and focusing purely on business rules.
/// 
/// The use case delegates the actual authentication to the repository layer
/// while handling any business-specific logic or validation.
class AppleLoginUseCaseImpl implements AppleLoginUseCase {
  final AuthRepository _authRepository;

  AppleLoginUseCaseImpl(this._authRepository);

  @override
  Future<Either<AuthFailure, UserEntity>> call() async {
    // Delegate to repository for Apple authentication
    // No additional business logic is required for Apple login
    // as the validation is handled by the Apple Sign-In flow
    return await _authRepository.authenticateWithApple();
  }
}
