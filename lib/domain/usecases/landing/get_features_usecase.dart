import 'package:dartz/dartz.dart';
import '../../entities/feature_entity.dart';
import '../../failures/landing_failure.dart';
import '../../repositories/landing_repository.dart';

/// Use case interface for retrieving app features.
/// 
/// This interface defines the contract for feature retrieval business logic
/// following Clean Architecture principles. It encapsulates the business rules
/// for loading and presenting app features on the landing page.
abstract class GetFeaturesUseCase {
  /// Executes the get features use case.
  /// 
  /// Returns [Right] with [List<FeatureEntity>] on successful data retrieval,
  /// or [Left] with [LandingFailure] on failure.
  Future<Either<LandingFailure, List<FeatureEntity>>> call();
}

/// Implementation of the get features use case.
/// 
/// This class encapsulates the business logic for retrieving app features.
/// It follows Clean Architecture principles by being independent of external
/// frameworks and focusing purely on business rules.
/// 
/// The use case delegates the actual data retrieval to the repository layer
/// while handling any business-specific logic, validation, or transformation.
class GetFeaturesUseCaseImpl implements GetFeaturesUseCase {
  final LandingRepository _landingRepository;

  GetFeaturesUseCaseImpl(this._landingRepository);

  @override
  Future<Either<LandingFailure, List<FeatureEntity>>> call() async {
    // Delegate to repository for feature data retrieval
    final result = await _landingRepository.getFeatures();
    
    // Apply any business logic transformations if needed
    return result.fold(
      (failure) => Left(failure),
      (features) {
        // Business rule: Ensure features are not empty
        if (features.isEmpty) {
          return Left(DataLoadFailure());
        }
        
        // Business rule: Sort features by title for consistent display
        final sortedFeatures = List<FeatureEntity>.from(features)
          ..sort((a, b) => a.title.compareTo(b.title));
        
        return Right(sortedFeatures);
      },
    );
  }
}
