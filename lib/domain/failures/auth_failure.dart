import 'package:collection/collection.dart';

/// Base class for authentication-related failures in the domain layer.
///
/// This follows Clean Architecture principles by defining domain-specific
/// error types that are independent of external frameworks or libraries.
/// All authentication failures inherit from this base class.
abstract class AuthFailure {
  final String message;
  const AuthFailure(this.message);

  @override
  bool operator ==(Object other) => identical(this, other) || other is AuthFailure && runtimeType == other.runtimeType && message == other.message;

  @override
  int get hashCode => message.hashCode;

  @override
  String toString() => 'AuthFailure{message: $message}';
}

/// Failure that occurs when input validation fails.
///
/// Contains a map of field names to error messages for detailed
/// validation feedback to the user interface.
class ValidationFailure extends AuthFailure {
  final Map<String, String> errors;

  const ValidationFailure(this.errors) : super('Validation failed');

  @override
  bool operator ==(Object other) =>
      identical(this, other) || other is ValidationFailure && runtimeType == other.runtimeType && const MapEquality().equals(errors, other.errors);

  @override
  int get hashCode => const MapEquality().hash(errors);

  @override
  String toString() => 'ValidationFailure{errors: $errors}';
}

/// Failure that occurs when authentication credentials are invalid.
///
/// This represents business logic failures where the user's credentials
/// are rejected by the authentication system.
class AuthenticationFailure extends AuthFailure {
  const AuthenticationFailure() : super('Authentication failed');
}

/// Failure that occurs when there are network connectivity issues.
///
/// This represents infrastructure failures that prevent communication
/// with external authentication services.
class NetworkFailure extends AuthFailure {
  const NetworkFailure() : super('Network error occurred');
}

/// Failure that occurs when the server returns an unexpected error.
///
/// This represents server-side failures that are not related to
/// authentication logic but prevent successful authentication.
class ServerFailure extends AuthFailure {
  const ServerFailure() : super('Server error occurred');
}

/// Failure that occurs for any other unexpected errors.
///
/// This is a catch-all failure type for errors that don't fit
/// into the other specific failure categories.
class UnknownFailure extends AuthFailure {
  const UnknownFailure(super.message);
}
