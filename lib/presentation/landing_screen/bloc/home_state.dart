part of 'home_bloc.dart';

class HomeState extends Equatable {
  final HomeModel? landingModel;
  final TextEditingController? emailController;
  final String? email;
  final bool isSubscribed;
  final String? selectedNavItem;
  final PricingPlanModel? selectedPlan;

  const HomeState({this.landingModel, this.emailController, this.email, this.isSubscribed = false, this.selectedNavItem, this.selectedPlan});

  @override
  List<Object?> get props => [landingModel, emailController, email, isSubscribed, selectedNavItem, selectedPlan];

  HomeState copyWith({
    HomeModel? landingModel,
    TextEditingController? emailController,
    String? email,
    bool? isSubscribed,
    String? selectedNavItem,
    PricingPlanModel? selectedPlan,
  }) {
    return HomeState(
      landingModel: landingModel ?? this.landingModel,
      emailController: emailController ?? this.emailController,
      email: email ?? this.email,
      isSubscribed: isSubscribed ?? this.isSubscribed,
      selectedNavItem: selectedNavItem ?? this.selectedNavItem,
      selectedPlan: selectedPlan ?? this.selectedPlan,
    );
  }
}
