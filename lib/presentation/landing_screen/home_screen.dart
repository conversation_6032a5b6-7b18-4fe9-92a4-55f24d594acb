import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

import '../../core/app_export.dart';
import '../../core/di/injection_container.dart' as di;
import '../../core/utils/app_colors.dart';
import '../../core/widgets/custom_button.dart';
import '../../core/widgets/custom_image_view.dart';
import '../../core/widgets/custom_input_field.dart';
import '../../l10n/app_localizations.dart';
import './widgets/app_store_button_widget.dart';
import './widgets/feature_item_widget.dart';
import './widgets/hero_section_widget.dart';
import './widgets/partner_logo_widget.dart';
import './widgets/pricing_card_widget.dart';
import 'bloc/home_bloc.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  static Widget builder(BuildContext context) {
    return BlocProvider<HomeBloc>(create: (context) => di.sl<HomeBloc>()..add(HomeInitialEvent()), child: const HomeScreen());
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      builder: (context, state) {
        return Container(
          color: Theme.of(context).colorScheme.surface,
          child: SingleChildScrollView(
            primary: true,
            child: Container(
              width: double.maxFinite,
              constraints: BoxConstraints(maxWidth: 375.h),
              child: Column(
                children: [
                  _buildHeaderSection(context, state),
                  HeroSectionWidget(state: state),
                  _buildPartnersSection(context, state),
                  _buildFeaturesSection(context, state),
                  _buildCtaSection(context, state),
                  _buildDivider(),
                  _buildPricingSection(context, state),
                  _buildDivider(),
                  _buildNewsletterSection(context, state),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeaderSection(BuildContext context, HomeState state) {
    return Container(
      width: double.maxFinite,
      padding: EdgeInsets.symmetric(horizontal: 16.h, vertical: 20.h),
      child: Column(
        children: [
          SizedBox(height: 60.h),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.h, vertical: 8.h),
            decoration: BoxDecoration(color: appTheme.colorFFF9F5FF, borderRadius: BorderRadius.circular(15.h)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 12.h, vertical: 4.h),
                      decoration: BoxDecoration(color: appTheme.whiteCustom, borderRadius: BorderRadius.circular(11.h)),
                      child: Text(
                        AppLocalizations.of(context)?.newFeature ?? 'New',
                        style: TextStyleHelper.instance.body12Medium.copyWith(color: appTheme.colorFF6840C6),
                      ),
                    ),
                    SizedBox(width: 8.h),
                    Text(AppStrings.personalizedCoachingInApp, style: TextStyleHelper.instance.body12Medium.copyWith(color: AppColors.primaryPurple)),
                  ],
                ),
                CustomImageView(imagePath: ImageConstant.imgArrowrightDeepPurple300, height: 16.h, width: 16.h),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPartnersSection(BuildContext context, HomeState state) {
    return Container(
      width: double.maxFinite,
      padding: EdgeInsets.symmetric(horizontal: 16.h, vertical: 32.h),
      color: Theme.of(context).scaffoldBackgroundColor,
      child: Column(
        children: [
          Text(AppStrings.officialPartnerOfTheseCompanies, style: TextStyleHelper.instance.title16Medium.copyWith(color: AppColors.textSecondary)),
          SizedBox(height: 32.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              PartnerLogoWidget(logoPath: ImageConstant.partnerLogoA),
              SizedBox(width: 32.h),
              PartnerLogoWidget(logoPath: ImageConstant.partnerLogoGreen),
            ],
          ),
          SizedBox(height: 24.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              PartnerLogoWidget(logoPath: ImageConstant.imgCompanyLogo),
              SizedBox(width: 32.h),
              PartnerLogoWidget(logoPath: ImageConstant.partnerLogoBlue),
              SizedBox(width: 32.h),
              PartnerLogoWidget(logoPath: ImageConstant.partnerLogoBlack),
            ],
          ),
          SizedBox(height: 24.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              PartnerLogoWidget(logoPath: ImageConstant.partnerLogoGray),
              SizedBox(width: 32.h),
              PartnerLogoWidget(logoPath: ImageConstant.partnerLogoBlueAlt),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturesSection(BuildContext context, HomeState state) {
    return Container(
      width: double.maxFinite,
      padding: EdgeInsets.symmetric(horizontal: 16.h, vertical: 32.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(AppStrings.features, style: TextStyleHelper.instance.body14SemiBold.copyWith(color: AppColors.primaryPurple)),
          SizedBox(height: 8.h),
          Text(
            AppLocalizations.of(context)?.unlockYourself ?? 'Unlock yourself',
            style: TextStyleHelper.instance.display30SemiBold.copyWith(color: appTheme.colorFF0F1728, height: 1.2),
          ),
          SizedBox(height: 16.h),
          Text(
            AppStrings.dailyPersonalizedFitnessDescription,
            style: TextStyleHelper.instance.headline18.copyWith(color: AppColors.textSecondary, height: 1.4),
          ),
          SizedBox(height: 48.h),
          Column(
            children:
                state.landingModel?.features
                    .map(
                      (feature) => Padding(
                        key: ValueKey('feature_${feature.title}'),
                        padding: EdgeInsets.only(bottom: 48.h),
                        child: FeatureItemWidget(icon: feature.icon, title: feature.title, description: feature.description),
                      ),
                    )
                    .toList() ??
                [],
          ),

          IntrinsicHeight(
            child: OverflowBox(
              maxWidth: MediaQuery.of(context).size.width,
              child: CustomImageView(imagePath: ImageConstant.phoneMockup2, fit: BoxFit.cover, height: 360.h),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCtaSection(BuildContext context, HomeState state) {
    return Container(
      width: double.maxFinite,
      margin: EdgeInsets.symmetric(horizontal: 16.h),
      padding: EdgeInsets.only(left: 16.h, right: 16.h, top: 40.h),
      decoration: BoxDecoration(color: appTheme.colorFF52379E, borderRadius: BorderRadius.circular(16.h)),
      child: Column(
        children: [
          Text(
            AppLocalizations.of(context)?.startFreeTrial ?? 'Start your free trial',
            style: TextStyleHelper.instance.display30SemiBold.copyWith(color: appTheme.whiteCustom, height: 1.2),
          ),
          SizedBox(height: 16.h),
          Text(
            AppStrings.personalPerformanceTrackingMadeEasy,
            style: TextStyleHelper.instance.headline18.copyWith(color: appTheme.colorFFE9D7FE, height: 1.4),
          ),
          SizedBox(height: 32.h),
          Row(
            children: [
              Expanded(
                child: AppStoreButtonWidget(
                  backgroundColor: appTheme.transparentCustom,
                  borderColor: appTheme.whiteCustom,
                  appleLogoPath: ImageConstant.imgAppleLogo,
                  downloadTextPath: ImageConstant.imgDownloadOnThe,
                  storeTextPath: ImageConstant.imgAppStore,
                  onPressed: () {
                    context.read<HomeBloc>().add(CtaAppStoreButtonPressedEvent());
                  },
                ),
              ),
              SizedBox(width: 16.h),
              Expanded(
                child: AppStoreButtonWidget(
                  backgroundColor: appTheme.transparentCustom,
                  borderColor: appTheme.whiteCustom,
                  appleLogoPath: ImageConstant.imgGooglePlayLogoWhiteA700,
                  downloadTextPath: ImageConstant.imgGetItOn,
                  storeTextPath: ImageConstant.imgGooglePlay,
                  onPressed: () {
                    context.read<HomeBloc>().add(CtaPlayStoreButtonPressedEvent());
                  },
                ),
              ),
            ],
          ),
          SizedBox(height: 32.h),
          IntrinsicHeight(
            child: OverflowBox(
              maxWidth: MediaQuery.of(context).size.width,
              child: CustomImageView(imagePath: ImageConstant.phoneMockup3, fit: BoxFit.cover, height: 280.h),
            ),
          ),
          // PhoneMockupWidget(
          //   backgroundImage: null,
          //   screenImage: ImageConstant.imgScreenMockupReplaceFill500x240,
          //   phoneFrameImage: null,
          //   signalIcon: ImageConstant.img941Black900,
          //   batteryIcon: ImageConstant.imgRightBlack900,
          //   height: 500.h,
          //   showComplexFrame: true,
          // ),
        ],
      ),
    );
  }

  Widget _buildDivider() {
    return Container(
      height: 1.h,
      margin: EdgeInsets.symmetric(horizontal: 16.h),
      color: AppColors.dividerGray,
    );
  }

  Widget _buildPricingSection(BuildContext context, HomeState state) {
    return Container(
      width: double.maxFinite,
      padding: EdgeInsets.symmetric(horizontal: 16.h, vertical: 64.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(AppStrings.upgrade, style: TextStyleHelper.instance.body14SemiBold.copyWith(color: AppColors.primaryPurple)),
          SizedBox(height: 8.h),
          Text(
            AppStrings.pricingPlansThatScaleWithYou,
            style: TextStyleHelper.instance.display30SemiBold.copyWith(color: AppColors.primaryPurpleDark, height: 1.3),
          ),
          SizedBox(height: 24.h),
          Text(
            AppStrings.simpleTransparentPricingDescription,
            style: TextStyleHelper.instance.headline18.copyWith(color: AppColors.textSecondary, height: 1.4),
          ),
          SizedBox(height: 32.h),
          Column(
            children:
                state.landingModel?.pricingPlans
                    .map(
                      (plan) => Padding(
                        key: ValueKey('pricing_plan_${plan.title}'),
                        padding: EdgeInsets.only(bottom: 32.h),
                        child: PricingCardWidget(
                          price: plan.price,
                          title: plan.title,
                          description: plan.description,
                          features: plan.features,
                          isPopular: plan.isPopular,
                          onGetStarted: () {
                            context.read<HomeBloc>().add(PricingPlanSelectedEvent(plan));
                          },
                        ),
                      ),
                    )
                    .toList() ??
                [],
          ),
        ],
      ),
    );
  }

  Widget _buildNewsletterSection(BuildContext context, HomeState state) {
    return Container(
      width: double.maxFinite,
      padding: EdgeInsets.symmetric(horizontal: 16.h, vertical: 64.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppStrings.beTheFirstToKnowWhenWeLaunch,
            style: TextStyleHelper.instance.display30SemiBold.copyWith(color: appTheme.colorFF0F1728, height: 1.3),
          ),
          SizedBox(height: 16.h),
          Text(
            AppStrings.stillBuildingSubscribeDescription,
            style: TextStyleHelper.instance.headline18.copyWith(color: AppColors.textSecondary, height: 1.4),
          ),
          SizedBox(height: 32.h),
          CustomInputField(
            hintText: AppStrings.enterYourEmail,
            controller: state.emailController,
            keyboardType: TextInputType.emailAddress,
            onChanged: (value) {
              context.read<HomeBloc>().add(EmailChangedEvent(value));
            },
          ),
          SizedBox(height: 16.h),
          RichText(
            text: TextSpan(
              style: TextStyleHelper.instance.body14.copyWith(color: AppColors.textSecondary),
              children: [
                TextSpan(text: AppStrings.weCareAboutYourDataIn),
                TextSpan(
                  text: AppStrings.privacyPolicy,
                  style: TextStyle(decoration: TextDecoration.underline),
                  recognizer: TapGestureRecognizer()..onTap = () {},
                ),
              ],
            ),
          ),
          SizedBox(height: 16.h),
          CustomButton(
            text: AppStrings.subscribe,
            onPressed: () {
              context.read<HomeBloc>().add(SubscribeButtonPressedEvent());
            },
          ),
          SizedBox(height: 64.h),
          IntrinsicHeight(
            child: OverflowBox(
              maxWidth: MediaQuery.of(context).size.width,
              child: CustomImageView(imagePath: ImageConstant.phoneMockup4, fit: BoxFit.cover, height: 360.h),
            ),
          ),
        ],
      ),
    );
  }
}
