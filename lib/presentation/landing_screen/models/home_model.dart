import '../../../core/app_export.dart';
import './feature_item_model.dart';
import './pricing_plan_model.dart';

class HomeModel extends Equatable {
  const HomeModel({this.features = const [], this.pricingPlans = const []});

  final List<FeatureItemModel> features;
  final List<PricingPlanModel> pricingPlans;

  HomeModel copyWith({List<FeatureItemModel>? features, List<PricingPlanModel>? pricingPlans}) {
    return HomeModel(features: features ?? this.features, pricingPlans: pricingPlans ?? this.pricingPlans);
  }

  @override
  List<Object?> get props => [features, pricingPlans];
}
