import '../../../core/app_export.dart';

class PricingPlanModel extends Equatable {
  const PricingPlanModel({this.price = '', this.title = '', this.description = '', this.features = const [], this.isPopular = false});

  final String price;
  final String title;
  final String description;
  final List<String> features;
  final bool isPopular;

  PricingPlanModel copyWith({String? price, String? title, String? description, List<String>? features, bool? isPopular}) {
    return PricingPlanModel(
      price: price ?? this.price,
      title: title ?? this.title,
      description: description ?? this.description,
      features: features ?? this.features,
      isPopular: isPopular ?? this.isPopular,
    );
  }

  @override
  List<Object?> get props => [price, title, description, features, isPopular];
}
