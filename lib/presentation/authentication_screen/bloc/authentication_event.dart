part of 'authentication_bloc.dart';

abstract class AuthenticationEvent extends Equatable {
  const AuthenticationEvent();

  @override
  List<Object?> get props => [];
}

class AuthenticationInitialEvent extends AuthenticationEvent {}

class EmailChangedEvent extends AuthenticationEvent {
  final String email;

  const EmailChangedEvent(this.email);

  @override
  List<Object?> get props => [email];
}

class PasswordChangedEvent extends AuthenticationEvent {
  final String password;

  const PasswordChangedEvent(this.password);

  @override
  List<Object?> get props => [password];
}

class TogglePasswordVisibilityEvent extends AuthenticationEvent {}

class LoginButtonTappedEvent extends AuthenticationEvent {}

class GoogleLoginTappedEvent extends AuthenticationEvent {}

class AppleLoginTappedEvent extends AuthenticationEvent {}

class ForgotPasswordTappedEvent extends AuthenticationEvent {}

class SignUpTappedEvent extends AuthenticationEvent {}
