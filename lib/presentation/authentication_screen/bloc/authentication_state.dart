part of 'authentication_bloc.dart';

enum ButtonAnimationPhase { rectangular, transitioningToCircular, circular, transitioningToRectangular }

class AuthenticationState extends Equatable {
  final TextEditingController? emailController;
  final TextEditingController? passwordController;
  final String? email;
  final String? password;
  final bool? isPasswordVisible;
  final bool? isLoading;
  final bool? isLoginSuccess;
  final bool? showError;
  final String? errorMessage;
  final String? emailError;
  final String? passwordError;
  final bool? isButtonAnimating;
  final ButtonAnimationPhase? buttonAnimationPhase;
  final AuthenticationModel? authenticationModel;

  const AuthenticationState({
    this.emailController,
    this.passwordController,
    this.email,
    this.password,
    this.isPasswordVisible,
    this.isLoading,
    this.isLoginSuccess,
    this.showError,
    this.errorMessage,
    this.emailError,
    this.passwordError,
    this.isButtonAnimating,
    this.buttonAnimationPhase,
    this.authenticationModel,
  });

  @override
  List<Object?> get props => [
    emailController,
    passwordController,
    email,
    password,
    isPasswordVisible,
    isLoading,
    isLoginSuccess,
    showError,
    errorMessage,
    emailError,
    passwordError,
    isButtonAnimating,
    buttonAnimationPhase,
    authenticationModel,
  ];

  AuthenticationState copyWith({
    TextEditingController? emailController,
    TextEditingController? passwordController,
    String? email,
    String? password,
    bool? isPasswordVisible,
    bool? isLoading,
    bool? isLoginSuccess,
    bool? showError,
    String? errorMessage,
    String? emailError,
    String? passwordError,
    bool? isButtonAnimating,
    ButtonAnimationPhase? buttonAnimationPhase,
    AuthenticationModel? authenticationModel,
    // Special flags to allow explicit null setting for error fields
    bool clearEmailError = false,
    bool clearPasswordError = false,
  }) {
    return AuthenticationState(
      emailController: emailController ?? this.emailController,
      passwordController: passwordController ?? this.passwordController,
      email: email ?? this.email,
      password: password ?? this.password,
      isPasswordVisible: isPasswordVisible ?? this.isPasswordVisible,
      isLoading: isLoading ?? this.isLoading,
      isLoginSuccess: isLoginSuccess ?? this.isLoginSuccess,
      showError: showError ?? this.showError,
      errorMessage: errorMessage ?? this.errorMessage,
      emailError: clearEmailError ? null : (emailError ?? this.emailError),
      passwordError: clearPasswordError ? null : (passwordError ?? this.passwordError),
      isButtonAnimating: isButtonAnimating ?? this.isButtonAnimating,
      buttonAnimationPhase: buttonAnimationPhase ?? this.buttonAnimationPhase,
      authenticationModel: authenticationModel ?? this.authenticationModel,
    );
  }
}
