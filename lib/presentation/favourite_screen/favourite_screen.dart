import 'package:flutter/material.dart';

import '../../core/app_export.dart';
import './bloc/favourite_bloc.dart';
import './models/favourite_model.dart';

class FavoriteScreen extends StatelessWidget {
  const FavoriteScreen({super.key});

  static Widget builder(BuildContext context) {
    return BlocProvider<FavoriteBloc>(
      create: (context) => FavoriteBloc(FavoriteState(favoriteModel: FavoriteModel()))..add(FavouriteInitialEvent()),
      child: const FavoriteScreen(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<FavoriteBloc, FavoriteState>(
      builder: (context, state) {
        return Container(
          color: Theme.of(context).colorScheme.surface,
          child: Center(child: Text('Favourite Screen', style: TextStyleHelper.instance.headline24SemiBold)),
        );
      },
    );
  }
}
