import '../../../core/app_export.dart';
import '../models/favourite_model.dart';

part 'favourite_event.dart';
part 'favourite_state.dart';

class FavoriteBloc extends Bloc<FavouriteEvent, FavoriteState> {
  FavoriteBloc(super.initialState) {
    on<FavouriteInitialEvent>(_onInitialize);
  }

  _onInitialize(FavouriteInitialEvent event, Emitter<FavoriteState> emit) async {
    emit(state.copyWith(favouriteModel: const FavoriteModel()));
  }
}
