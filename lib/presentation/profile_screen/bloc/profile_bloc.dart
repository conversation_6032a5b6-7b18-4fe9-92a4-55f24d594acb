import '../../../core/app_export.dart';
import '../../../core/utils/default_user_data.dart';
import '../models/profile_model.dart';

part 'profile_event.dart';
part 'profile_state.dart';

class ProfileBloc extends Bloc<ProfileEvent, ProfileState> {
  ProfileBloc(super.initialState) {
    on<ProfileInitialEvent>(_onInitialize);
    on<EditProfileImageEvent>(_onEditProfileImage);
    on<NavigateToEditProfileEvent>(_onNavigateToEditProfile);
    on<NavigateToAddressEvent>(_onNavigateToAddress);
    on<NavigateToHistoryEvent>(_onNavigateToHistory);
    on<NavigateToComplainEvent>(_onNavigateToComplain);
    on<NavigateToReferralEvent>(_onNavigateToReferral);
    on<NavigateToAboutUsEvent>(_onNavigateToAboutUs);
    on<NavigateToSettingsEvent>(_onNavigateToSettings);
    on<NavigateToHelpSupportEvent>(_onNavigateToHelpSupport);
    on<LogoutEvent>(_onLogout);
  }

  _onInitialize(ProfileInitialEvent event, Emitter<ProfileState> emit) async {
    emit(
      state.copyWith(
        profileModel: ProfileModel(
          userName: DefaultUserData.defaultUserName,
          userEmail: DefaultUserData.defaultUserEmail,
          profileImage: ImageConstant.defaultProfileAvatar,
        ),
      ),
    );
  }

  _onEditProfileImage(EditProfileImageEvent event, Emitter<ProfileState> emit) async {
    // Handle profile image editing logic
  }

  _onNavigateToEditProfile(NavigateToEditProfileEvent event, Emitter<ProfileState> emit) async {
    // Navigate to edit profile screen
  }

  _onNavigateToAddress(NavigateToAddressEvent event, Emitter<ProfileState> emit) async {
    // Navigate to address screen
  }

  _onNavigateToHistory(NavigateToHistoryEvent event, Emitter<ProfileState> emit) async {
    // Navigate to history screen
  }

  _onNavigateToComplain(NavigateToComplainEvent event, Emitter<ProfileState> emit) async {
    // Navigate to complain screen
  }

  _onNavigateToReferral(NavigateToReferralEvent event, Emitter<ProfileState> emit) async {
    // Navigate to referral screen
  }

  _onNavigateToAboutUs(NavigateToAboutUsEvent event, Emitter<ProfileState> emit) async {
    // Navigate to about us screen
  }

  _onNavigateToSettings(NavigateToSettingsEvent event, Emitter<ProfileState> emit) async {
    // Navigate to settings screen
  }

  _onNavigateToHelpSupport(NavigateToHelpSupportEvent event, Emitter<ProfileState> emit) async {
    // Navigate to help support screen
  }

  _onLogout(LogoutEvent event, Emitter<ProfileState> emit) async {
    // Handle logout functionality
    // Show confirmation dialog and perform logout
  }
}
