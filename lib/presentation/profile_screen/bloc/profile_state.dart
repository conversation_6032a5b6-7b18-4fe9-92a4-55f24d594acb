part of 'profile_bloc.dart';

class ProfileState extends Equatable {
  final ProfileModel? profileModel;
  final bool isLoading;

  const ProfileState({
    this.profileModel,
    this.isLoading = false,
  });

  @override
  List<Object?> get props => [
        profileModel,
        isLoading,
      ];

  ProfileState copyWith({
    ProfileModel? profileModel,
    bool? isLoading,
  }) {
    return ProfileState(
      profileModel: profileModel ?? this.profileModel,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}
