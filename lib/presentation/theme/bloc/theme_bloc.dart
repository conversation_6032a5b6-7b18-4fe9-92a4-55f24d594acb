import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../core/app_export.dart';

part 'theme_event.dart';
part 'theme_state.dart';

/// Manages application theme state and persistence.
///
/// This BLoC handles switching between light and dark themes, persisting
/// the user's theme preference to SharedPreferences, and loading the saved
/// theme on app startup. It ensures theme consistency across app restarts
/// and provides error handling for storage operations.
///
/// Key responsibilities:
/// - Theme state management (light/dark mode)
/// - Theme preference persistence via SharedPreferences
/// - Theme loading on app initialization
/// - Error handling for storage failures
class ThemeBloc extends Bloc<ThemeEvent, ThemeState> {
  final SharedPreferences prefs;
  static const String _themeKey = 'isDarkMode';

  ThemeBloc(this.prefs) : super(const ThemeState()) {
    on<LoadThemeEvent>(_onLoadTheme);
    on<ToggleThemeEvent>(_onToggleTheme);
    on<SetThemeEvent>(_onSetTheme);
  }

  Future<void> _onLoadTheme(LoadThemeEvent event, Emitter<ThemeState> emit) async {
    final isDarkMode = prefs.getBool(_themeKey) ?? false;
    emit(state.copyWith(isDarkMode: isDarkMode));
  }

  Future<void> _onToggleTheme(ToggleThemeEvent event, Emitter<ThemeState> emit) async {
    final newTheme = !state.isDarkMode;
    try {
      await prefs.setBool(_themeKey, newTheme);
      emit(state.copyWith(isDarkMode: newTheme));
    } catch (e) {
      debugPrint('Failed to save theme preference: $e');
      // Keep current state - don't update UI if save failed
    }
  }

  Future<void> _onSetTheme(SetThemeEvent event, Emitter<ThemeState> emit) async {
    try {
      await prefs.setBool(_themeKey, event.isDarkMode);
      emit(state.copyWith(isDarkMode: event.isDarkMode));
    } catch (e) {
      debugPrint('Failed to save theme preference: $e');
      // Keep current state - don't update UI if save failed
    }
  }
}
