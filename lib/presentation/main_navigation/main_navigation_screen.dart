import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_project/core/widgets/custom_appbar.dart';

import '../../core/app_export.dart';
import '../../core/di/injection_container.dart' as di;
import '../../core/widgets/custom_image_view.dart';
import '../favourite_screen/favourite_screen.dart';
import '../landing_screen/home_screen.dart';
import '../news_screen/news_screen.dart';
import '../notification_screen/notification_screen.dart';
import '../profile_screen/profile_screen.dart';
import './bloc/drawer_bloc.dart';

class MainNavigationScreen extends StatefulWidget {
  const MainNavigationScreen({super.key});

  static Widget builder(BuildContext context) {
    return BlocProvider<DrawerBloc>.value(value: di.sl<DrawerBloc>(), child: const MainNavigationScreen());
  }

  @override
  State<MainNavigationScreen> createState() => _MainNavigationScreenState();
}

class _MainNavigationScreenState extends State<MainNavigationScreen> with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    // Initialize with Home tab selected (index 0)
    _tabController = TabController(length: 4, initialIndex: 0, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DrawerBloc, DrawerState>(
      builder: (context, drawerState) {
        return Scaffold(
          appBar: CustomAppBar(),
          backgroundColor: Theme.of(context).colorScheme.surface,
          drawerScrimColor: Colors.transparent,
          endDrawer: SizedBox(width: MediaQuery.of(context).size.width * 0.66, child: ProfileScreen.builder(context, isDrawer: false)),
          onEndDrawerChanged: (isOpened) {
            if (isOpened) {
              context.read<DrawerBloc>().add(OpenDrawerEvent());
            } else {
              context.read<DrawerBloc>().add(CloseDrawerEvent());
            }
          },
          body: Stack(
            children: [
              Column(
                children: [
                  // TabBarView takes up remaining space
                  Expanded(
                    child: TabBarView(
                      physics: NeverScrollableScrollPhysics(),
                      controller: _tabController,
                      children: [
                        KeepAliveWrapper(child: HomeScreen.builder(context)),
                        KeepAliveWrapper(child: FavoriteScreen.builder(context)),
                        KeepAliveWrapper(child: NewsScreen.builder(context)),
                        KeepAliveWrapper(child: NotificationScreen.builder(context)),
                      ],
                    ),
                  ),
                  // Fixed TabBar at bottom
                  _buildBottomNavigation(context),
                ],
              ),
              // Background blur overlay when drawer is open
              if (drawerState.isOpen)
                Positioned.fill(
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 1.5, sigmaY: 1.5),
                    child: Container(
                      width: double.infinity,
                      height: double.infinity,
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.15),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildBottomNavigation(BuildContext context) {
    return Container(
      alignment: Alignment.topCenter,

      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(color: appTheme.colorFFE5E7EB, width: 1.h),
        ),
        borderRadius: BorderRadius.only(topLeft: Radius.circular(12.h), topRight: Radius.circular(12.h)),
      ),
      child: SafeArea(
        top: false,
        left: false,
        right: false,
        child: TabBar(
          isScrollable: false,
          controller: _tabController,
          // padding: EdgeInsets.symmetric(vertical: 8.h),
          indicator: BoxDecoration(), // Transparent indicator
          labelColor: Theme.of(context).tabBarTheme.labelColor,
          unselectedLabelColor: Theme.of(context).tabBarTheme.unselectedLabelColor,
          labelStyle: Theme.of(context).tabBarTheme.labelStyle,
          unselectedLabelStyle: Theme.of(context).tabBarTheme.unselectedLabelStyle,
          indicatorSize: TabBarIndicatorSize.label,
          dividerColor: Theme.of(context).tabBarTheme.dividerColor,
          tabs: [
            _buildCustomTab(ImageConstant.imgHouse, 'Home'),
            _buildCustomTab(ImageConstant.imgHeart, 'Favourite'),
            _buildCustomTab(ImageConstant.imgNotepad, 'News'),
            _buildCustomTab(ImageConstant.imgBellsimple, 'Notification'),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomTab(String iconPath, String label) {
    return Tab(
      height: 50.h,
      child: Builder(
        builder: (context) {
          final iconColor = IconTheme.of(context).color;
          return Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CustomImageView(imagePath: iconPath, height: 24.h, width: 24.h, color: iconColor),
              SizedBox(height: 4.h),
              Flexible(
                child: Text(
                  label,
                  style: TextStyle(fontSize: 10.fSize),
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}

class KeepAliveWrapper extends StatefulWidget {
  final Widget child;
  final bool keepAlive;
  const KeepAliveWrapper({super.key, required this.child, this.keepAlive = true});

  @override
  State<KeepAliveWrapper> createState() => _KeepAliveWrapperState();
}

class _KeepAliveWrapperState extends State<KeepAliveWrapper> with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => widget.keepAlive;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return widget.child;
  }
}
