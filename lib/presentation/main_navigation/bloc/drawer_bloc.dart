import '../../../core/app_export.dart';

part 'drawer_event.dart';
part 'drawer_state.dart';

class DrawerBloc extends Bloc<DrawerEvent, DrawerState> {
  DrawerBloc() : super(const DrawerState()) {
    on<OpenDrawerEvent>(_onOpenDrawer);
    on<CloseDrawerEvent>(_onCloseDrawer);
    on<ToggleDrawerEvent>(_onToggleDrawer);
  }

  _onOpenDrawer(OpenDrawerEvent event, Emitter<DrawerState> emit) async {
    emit(state.copyWith(isOpen: true));
  }

  _onCloseDrawer(CloseDrawerEvent event, Emitter<DrawerState> emit) async {
    emit(state.copyWith(isOpen: false));
  }

  _onToggleDrawer(ToggleDrawerEvent event, Emitter<DrawerState> emit) async {
    emit(state.copyWith(isOpen: !state.isOpen));
  }
}
