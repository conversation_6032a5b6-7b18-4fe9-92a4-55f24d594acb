import 'package:flutter/material.dart';

import '../../../core/app_export.dart';
import '../../../core/widgets/custom_image_view.dart';
import '../models/account_model.dart';

// Modified: Removed non-existent import and using ProfileMenuItemModel from profile_model.dart

class ProfileMenuItemWidget extends StatelessWidget {
  final ProfileMenuItemModel menuItem;
  final VoidCallback? onTap;
  final bool isLastItem;

  const ProfileMenuItemWidget({super.key, required this.menuItem, this.onTap, this.isLastItem = false});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.h, vertical: 16.h),
        decoration: BoxDecoration(
          color: appTheme.whiteCustom,
          border: isLastItem
              ? null
              : Border(
                  bottom: BorderSide(color: appTheme.colorFFE0E0, width: 1.h),
                ),
        ),
        child: Row(
          children: [
            CustomImageView(imagePath: menuItem.icon ?? '', height: 20.h, width: 20.h),
            SizedBox(width: 16.h),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(menuItem.title ?? '', style: TextStyleHelper.instance.body14Medium),
                  if (menuItem.subtitle?.isNotEmpty ?? false) ...[
                    SizedBox(height: 2.h),
                    Text(menuItem.subtitle ?? '', style: TextStyleHelper.instance.body12),
                  ],
                ],
              ),
            ),
            CustomImageView(imagePath: ImageConstant.arrowRightIcon, height: 16.h, width: 16.h),
          ],
        ),
      ),
    );
  }
}
