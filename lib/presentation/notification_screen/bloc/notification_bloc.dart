import '../../../core/app_export.dart';
import '../models/notification_model.dart';

part 'notification_event.dart';
part 'notification_state.dart';

class NotificationBloc extends Bloc<NotificationEvent, NotificationState> {
  NotificationBloc(super.initialState) {
    on<NotificationInitialEvent>(_onInitialize);
  }

  _onInitialize(NotificationInitialEvent event, Emitter<NotificationState> emit) async {
    emit(state.copyWith(notificationModel: const NotificationModel()));
  }
}
