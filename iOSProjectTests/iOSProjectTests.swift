//
//  iOSProjectTests.swift
//  iOSProjectTests
//
//  Created by Apple on 08/08/2025.
//

import Testing
import Foundation
@testable import iOSProject

// MARK: - User Model Tests
struct UserModelTests {

    @Test func testUserInitialization() async throws {
        let user = User(email: "<EMAIL>", name: "Test User")

        #expect(user.email == "<EMAIL>")
        #expect(user.name == "Test User")
        #expect(!user.id.isEmpty)
    }

    @Test func testUserDisplayName() async throws {
        let userWithName = User(email: "<EMAIL>", name: "<PERSON>")
        #expect(userWithName.displayName == "<PERSON>")

        let userWithoutName = User(email: "<EMAIL>", name: "")
        #expect(userWithoutName.displayName == "<EMAIL>")
    }

    @Test func testUserInitials() async throws {
        let user = User(email: "<EMAIL>", name: "<PERSON>")
        #expect(user.initials == "JD")

        let singleName = User(email: "<EMAIL>", name: "<PERSON>")
        #expect(singleName.initials == "J")
    }
}

// MARK: - NewsItem Model Tests
struct NewsItemModelTests {

    @Test func testNewsItemInitialization() async throws {
        let newsItem = NewsItem(
            title: "Test News",
            description: "Test Description",
            content: "Test Content",
            author: "Test Author",
            category: .technology
        )

        #expect(newsItem.title == "Test News")
        #expect(newsItem.description == "Test Description")
        #expect(newsItem.author == "Test Author")
        #expect(newsItem.category == .technology)
        #expect(!newsItem.id.isEmpty)
    }

    @Test func testNewsItemTimeAgo() async throws {
        let now = Date()

        let justNowItem = NewsItem(
            title: "Test News",
            description: "Test Description",
            content: "Test Content",
            author: "Test Author",
            publishedAt: now.addingTimeInterval(-30),
            category: .technology
        )
        #expect(justNowItem.timeAgo == "Just now")

        let minutesAgoItem = NewsItem(
            title: "Test News",
            description: "Test Description",
            content: "Test Content",
            author: "Test Author",
            publishedAt: now.addingTimeInterval(-300),
            category: .technology
        )
        #expect(minutesAgoItem.timeAgo == "5 minutes ago")
    }

    @Test func testNewsItemFormattedDate() async throws {
        let testDate = Date()
        let newsItem = NewsItem(
            title: "Test News",
            description: "Test Description",
            content: "Test Content",
            author: "Test Author",
            publishedAt: testDate,
            category: .technology
        )

        #expect(!newsItem.formattedPublishedDate.isEmpty)
    }
}

// MARK: - NewsCategory Tests
struct NewsCategoryTests {

    @Test func testNewsCategoryProperties() async throws {
        #expect(NewsCategory.technology.displayName == "Technology")
        #expect(NewsCategory.technology.color == "7E56D8")
        #expect(NewsCategory.technology.rawValue == "technology")
    }

    @Test func testNewsCategoryAllCases() async throws {
        let allCases = NewsCategory.allCases
        #expect(allCases.count == 7)
        #expect(allCases.contains(.technology))
        #expect(allCases.contains(.business))
    }
}

// MARK: - FeatureItem Tests
struct FeatureItemTests {

    @Test func testFeatureItemInitialization() async throws {
        let featureItem = FeatureItem(
            icon: "chart.pie",
            title: "Test Feature",
            description: "Test Description"
        )

        #expect(featureItem.icon == "chart.pie")
        #expect(featureItem.title == "Test Feature")
        #expect(featureItem.description == "Test Description")
        #expect(!featureItem.id.uuidString.isEmpty)
    }

    @Test func testFeatureItemEquality() async throws {
        let featureItem1 = FeatureItem(
            icon: "chart.pie",
            title: "Test Feature",
            description: "Test Description"
        )

        #expect(featureItem1 == featureItem1)
    }
}

// MARK: - PricingPlan Tests
struct PricingPlanTests {

    @Test func testPricingPlanInitialization() async throws {
        let pricingPlan = PricingPlan(
            price: "$10/mth",
            title: "Basic Plan",
            description: "Billed annually",
            isPopular: true,
            features: ["Feature 1", "Feature 2"]
        )

        #expect(pricingPlan.price == "$10/mth")
        #expect(pricingPlan.title == "Basic Plan")
        #expect(pricingPlan.isPopular == true)
        #expect(pricingPlan.features.count == 2)
        #expect(!pricingPlan.id.uuidString.isEmpty)
    }

    @Test func testPricingPlanEquality() async throws {
        let plan1 = PricingPlan(
            price: "$10/mth",
            title: "Basic Plan",
            description: "Billed annually",
            isPopular: true,
            features: ["Feature 1"]
        )

        #expect(plan1 == plan1)
    }
}

// MARK: - PartnerLogo Tests
struct PartnerLogoTests {

    @Test func testPartnerLogoInitialization() async throws {
        let partnerLogo = PartnerLogo(
            logoName: "apple.logo",
            companyName: "Apple"
        )

        #expect(partnerLogo.logoName == "apple.logo")
        #expect(partnerLogo.companyName == "Apple")
        #expect(!partnerLogo.id.uuidString.isEmpty)
    }
}

// MARK: - LandingData Tests
struct LandingDataTests {

    @Test func testLandingDataInitialization() async throws {
        let features = [
            FeatureItem(icon: "icon1", title: "Feature 1", description: "Description 1")
        ]

        let pricingPlans = [
            PricingPlan(price: "$10", title: "Basic", description: "Basic plan", isPopular: true, features: ["Feature 1"])
        ]

        let partnerLogos = [
            PartnerLogo(logoName: "logo1", companyName: "Company 1")
        ]

        let landingData = LandingData(
            features: features,
            pricingPlans: pricingPlans,
            partnerLogos: partnerLogos
        )

        #expect(landingData.features.count == 1)
        #expect(landingData.pricingPlans.count == 1)
        #expect(landingData.partnerLogos.count == 1)
    }

    @Test func testLandingDataDefault() async throws {
        let defaultData = LandingData.default

        #expect(defaultData.features.count == 4)
        #expect(defaultData.pricingPlans.count == 2)
        #expect(defaultData.partnerLogos.count == 6)
    }
}